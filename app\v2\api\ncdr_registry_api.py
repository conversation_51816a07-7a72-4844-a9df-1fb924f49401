from fastapi import APIRouter, Request, Response, Depends, status, HTTPException
from pymongo.database import Database, DBRef
from bson import ObjectId
import xml.etree.ElementTree as ET
import base64
import shutil
import zeep,pyzipper
import os, io
from config import settings
from v2.schema import ResponseSchema
from v2.utils import (authorizer, api_response,
                    get_field_ids_and_values,
                    compute_hash,zip_and_encrypt_file,
                    unzip_and_decrypt_file, validate_xml, construct_ncdr_xml)

router = APIRouter(tags=["V2 Submission"], prefix="/ncdr/cases")

wsdl = settings.NCDR_WSDL
client = zeep.Client(wsdl=wsdl)

client_id = settings.NCDR_CLIENT_ID
client_id_bytes = client_id.encode("ascii")

registry = settings.NCDR_REGISTRY
secrets = settings.NCDR_SECRETS
enc_algo = settings.NCDR_ENC_ALGORITHM

@router.post("/{case_id}/submit", response_model=ResponseSchema)
async def submit_case(
    request: Request,
    response: Response,
    case_id: str,
    token_data: dict = Depends(authorizer),
):
    try:
        mongo_client: Database = request.app.database
        case_data = mongo_client.patient_registry_details.find_one({"case_id": DBRef("case_summary", ObjectId(case_id))}, {"_id" : 0, "created_at" : 0, "deleted_at":0,"updated_at" : 0})
        if not case_data:
            raise HTTPException(status_code=404, detail="case not found")
        
        if case_data:
            case_data.pop("case_id", None) 
            filtered_case_data = case_data  
        

        # updated_xml_path = get_field_ids_and_values(filtered_case_data, case_id)
        updated_xml_path = construct_ncdr_xml(filtered_case_data, case_id)
        if not updated_xml_path:
            raise HTTPException(status_code=500, detail="Failed to update XML file")
        validation_errors = validate_xml(updated_xml_path)
        if validation_errors:
            raise HTTPException(status_code=400, detail=validation_errors)
        
        zip_file_path = zip_and_encrypt_file(updated_xml_path,case_id,secrets)
        zip_file_path = zip_file_path.replace("/", "//")

        zip_file_basename= os.path.basename(zip_file_path)

        print(zip_file_path)
        with open(zip_file_path, "rb") as file:
            file_data = file.read()
        encoded_file_data = base64.b64encode(file_data).decode("ascii")

        submission_response = client.service.DQRSubmission(
            client_id,
            registry,
            compute_hash(secrets, enc_algo, client_id_bytes),
            zip_file_basename,
            encoded_file_data
        )
        
        mongo_client.ncdr_registry_status.update_one({"case_id": DBRef("case_summary", case_id)},
            {"$set":{"status": submission_response.Status,
            "submission_id": submission_response.SubmissionID,
            "case_id": DBRef("case_summary", case_id)}},
            upsert=True
            )
        # folder_path = os.path.abspath(rf"v2\data_sources\xml\{case_id}")
        # if os.path.exists(folder_path):
        #     shutil.rmtree(folder_path)
        response.status_code = status.HTTP_200_OK
        return api_response({"status " : submission_response.Status, "submission_id" : submission_response.SubmissionID }, "Submission successful", "success")
    except Exception as e:
        raise

@router.post("/{case_id}/status", response_model=ResponseSchema)
async def get_ncdr_case_status(
    request: Request,
    response: Response,
    case_id: str,
    token_data: dict = Depends(authorizer),
):
    try:
        mongo_client: Database = request.app.database
        case_object_id = ObjectId(case_id)
        status_data = mongo_client.ncdr_registry_status.find_one({"case_id.$id": case_id})
        submission_id = status_data.get("submission_id")
        status_response = client.service.DQRSubmissionResultBySubmissionGuid(
            client_id,registry,compute_hash(secrets,enc_algo, client_id_bytes),submission_id)
        zip_file = io.BytesIO(status_response.Result)
        result = unzip_and_decrypt_file(zip_file, secrets)
        status_id = status_data.get("_id")
        mongo_client.ncdr_registry_status.update_one(
            {"_id" : ObjectId(status_id)},
            {"$set" : {"result" : str(result)}}
            )

        response.status_code = status.HTTP_200_OK
        return api_response({"status " : status_response.Status, "result" : result}, "Submission successful", "success")

    except Exception as e:
        raise
