#!/usr/bin/env python3
"""
Minimal test for nested field extraction logic
"""

# Copy the extraction function here to test it independently
def extract_fields_with_label(data_dict):
    """
    Traverse through a dictionary and extract fields with a 'label' key.
    Also handles nested conditional fields in if_yes, if_hemorrhage, if_alive, if_deceased.
    """
    result = []
    table_ids = ["14839_container"]  # Simplified for test

    def traverse(obj, path=None, parent_value=None):
        if path is None:
            path = []

        if isinstance(obj, dict):
            # Check if this is a field with a label
            if 'label' in obj:
                if obj.get('field_id', None) not in table_ids:
                    field_info = {
                        'field_id': obj.get('field_id', None),
                        'label': obj.get('label', None),
                        'value': obj.get('value', None),
                        'options': obj.get('options', None),
                        'input_type': obj.get('input_type', None)
                    }
                    result.append(field_info)
                    
                    # Handle nested conditional fields
                    current_value = obj.get('value', None)
                    
                    # Process if_yes when value is "Yes" or True
                    if current_value in ["Yes", "yes", True, "true"] and 'if_yes' in obj:
                        traverse(obj['if_yes'], path + ['if_yes'], current_value)
                    
                    # Process if_hemorrhage (special case for hemorrhage fields)
                    if 'if_hemorrhage' in obj:
                        traverse(obj['if_hemorrhage'], path + ['if_hemorrhage'], current_value)
                    
                    # Process if_alive when value is "Alive"
                    if current_value in ["Alive", "alive"] and 'if_alive' in obj:
                        traverse(obj['if_alive'], path + ['if_alive'], current_value)
                    
                    # Process if_deceased when value is "Deceased"
                    if current_value in ["Deceased", "deceased"] and 'if_deceased' in obj:
                        traverse(obj['if_deceased'], path + ['if_deceased'], current_value)

            # Continue traversing all key-value pairs
            for key, value in obj.items():
                if key == "field_id" and value in table_ids:
                    result.append(obj)
                    break
                # Skip conditional blocks that are already processed above
                if key not in ['if_yes', 'if_hemorrhage', 'if_alive', 'if_deceased']:
                    new_path = path + [key]
                    traverse(value, new_path, parent_value)

        elif isinstance(obj, list):
            for item in obj:
                traverse(item, path, parent_value)

    traverse(data_dict)
    return result

# Test data with nested if_yes structure
test_data = {
    "left_atrial_appendage_occlusion_intervention": {
        "field_id": "14804",
        "label": "Left Atrial Appendage Occlusion Intervention",
        "options": ["No", "Yes"],
        "input_type": "multi_input_field",
        "if_yes": {
            "left_atrial_appendage_intervention_type": {
                "field_id": "14806",
                "label": "Left Atrial Appendage Intervention Type",
                "options": [
                    {"id": "1", "value": "Epicardial Ligation"},
                    {"id": "2", "value": "Surgical Amputation"},
                    {"id": "3", "value": "Surgical Ligation"},
                    {"id": "4", "value": "Percutaneous Occlusion"},
                    {"id": "5", "value": "Surgical Closure Device"},
                    {"id": "6", "value": "Surgical Stapling"}
                ],
                "input_type": "multi_select",
                "value": ["Surgical Amputation"],
                "modified_by": "ABSTRACTOR",
                "value_source": "Embeddings"
            }
        },
        "value": "Yes",
        "modified_by": "ABSTRACTOR",
        "value_source": "Embeddings"
    }
}

def test_extraction():
    print("Testing nested field extraction...")
    
    # Extract fields
    extracted_fields = extract_fields_with_label(test_data)
    
    print(f"\nExtracted {len(extracted_fields)} fields:")
    for field in extracted_fields:
        print(f"  Field ID: {field['field_id']}, Value: {field['value']}, Label: {field['label']}")
    
    # Check if both parent and nested fields are extracted
    field_ids = [f['field_id'] for f in extracted_fields]
    
    expected_fields = ['14804', '14806']
    missing_fields = [fid for fid in expected_fields if fid not in field_ids]
    
    if missing_fields:
        print(f"\n❌ FAILED: Missing field IDs: {missing_fields}")
        return False
    else:
        print(f"\n✅ SUCCESS: All expected fields extracted")
        
        # Check values
        field_14804 = next((f for f in extracted_fields if f['field_id'] == '14804'), None)
        field_14806 = next((f for f in extracted_fields if f['field_id'] == '14806'), None)
        
        if field_14804 and field_14804['value'] == 'Yes':
            print(f"✅ Field 14804 has correct value: {field_14804['value']}")
        else:
            print(f"❌ Field 14804 has incorrect value: {field_14804['value'] if field_14804 else 'None'}")
            
        if field_14806 and field_14806['value'] == ['Surgical Amputation']:
            print(f"✅ Field 14806 has correct value: {field_14806['value']}")
        else:
            print(f"❌ Field 14806 has incorrect value: {field_14806['value'] if field_14806 else 'None'}")
        
        return True

if __name__ == "__main__":
    success = test_extraction()
    
    if success:
        print(f"\n🎉 Extraction test passed!")
    else:
        print(f"\n💥 Extraction test failed!")
