from fastapi import APIRouter, Request, Response, Depends, status, HTTPException
from v2.schema import (
    ResponseSchema,
    CreateNewPatient,
    UpdatePatientSchema,
    CreateBasePatient,
    HasbledScoreSchema,
    ChadscoreSchema,
    User
)
from v2.utils import (
    api_response,
    authorizer,
    embedded_data_points_agent,
    get_default_medication,
    get_summary_by_patient_id,
    get_patient_details_by_case_id
)
from v2.constants import REPROLE
from pymongo.database import Database, DBRef
from bson.objectid import ObjectId
from datetime import datetime
from .create_patient_json import create_patient_using_json
from v2.constants import LAAO_PROCEDURE

router = APIRouter(tags=["V2 Patient API"])


@router.post("/sites/{site_id}/patients", response_model=ResponseSchema)
async def create_new_patient(
    request: Request,
    response: Response,
    body: CreateNewPatient,
    site_id: str,
    token_data: dict = Depends(authorizer),
):
    try:
        parsed_data = body.model_dump()
        # procedure_id = parsed_data.pop("procedure_type_id")
        mongo_client: Database = request.app.database

        # Get LAAO Procedure type id/reps/schedule/cases/${case_id}/patient
        pt_collection = mongo_client.get_collection("procedure_type")
        pt_data = pt_collection.find_one({"name": LAAO_PROCEDURE})
        
        if pt_data:
            procedure_type_id = str(pt_data.get("_id"))

        patient_collection = mongo_client.get_collection("patient")
        rationale = parsed_data.pop("rationale", None)
        rationale_other = parsed_data.pop("rationale_other", None)
        procedure_date = parsed_data.pop("procedure_date", None)
        procedure_time = parsed_data.pop("procedure_time", None)
        implanting_physician_id = parsed_data.pop("implanting_physician", None)

        add_patient = patient_collection.insert_one(parsed_data)
        patient_id = add_patient.inserted_id
        # patient_id = ObjectId()

        default_anticoagulation = get_default_medication(mongo_client, True)

        case_summary_collection = mongo_client.get_collection("case_summary")
        case_detail = {
            "procedure_type_id": DBRef("procedure_type", ObjectId(procedure_type_id)),
            "patient_id": DBRef("patient", ObjectId(patient_id)),
            "site_id": DBRef("site", ObjectId(site_id)),
            "implanting_physician_id": implanting_physician_id,
            "rep_id": None,
            "procedure_date": procedure_date,
            "procedure_time": procedure_time,
            "rationale": rationale,
            "rationale_other": rationale_other,
            "cta": False,
            "tee": False,
            "afib_ablation": False,
            "prior_ablation": None,
            "prior_ablation_other": None,
            "anticoagulant": default_anticoagulation,
            "truplan_pdf_filename": None,
            "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "deleted_at": None,
            "secondary_rationale": None,
            "secondary_rationale_other": None,
            "complication": None,
            "afib_classification_type_id": None,
            "truplan_upload_status": "Pending",
        }
        user_data = User(**token_data.get("data", {}))
        if REPROLE in user_data.realm_access.get("roles", []):
            keycloak_id = user_data.sub
            user_collection = mongo_client.get_collection("user")
            data = user_collection.find_one({"deleted_at": None, "keycloak_id": keycloak_id})
            case_detail['rep_id'] = DBRef("user", ObjectId(data['_id']))

        create_case = case_summary_collection.insert_one(case_detail)
        case_id = create_case.inserted_id
        # case_id = ObjectId()

        create_patient_body = CreateBasePatient(
            **parsed_data, case_id=str(case_id), patient_id=str(patient_id)
        )

        # Call the second endpoint directly
        second_api_response = await create_patient_using_json(
            request=request,
            response=response,
            body=create_patient_body,
            token_data=token_data,
        )
        embedded_data_points_agent(str(case_id), token_data["token"])
        return_response = second_api_response["result"][0]

        response.status_code = status.HTTP_201_CREATED
        return api_response([return_response], "New patient case created", "success")
    except Exception:
        raise


@router.put("/patient/{case_id}")
async def update_patient_record(
    request: Request,
    response: Response,
    body: UpdatePatientSchema,
    case_id: str,
    token_data: dict = Depends(authorizer),
):
    try:
        filtered_data = body.model_dump(exclude_unset=True)
        # filtered_data = {k: v for k, v in update_values.items() if v }
        mongo_client: Database = request.app.database
        if filtered_data:
            patient_data = get_patient_details_by_case_id(case_id, mongo_client)
            patient_id = patient_data['patient_id']
            patient_collection = mongo_client.get_collection("patient")
            update_patient = patient_collection.update_one(
                {"_id": ObjectId(patient_id)}, {"$set": filtered_data}
            )
            if update_patient.matched_count > 0:
                case_summary_data = get_summary_by_patient_id(patient_id, mongo_client)
                case_id = str(case_summary_data["_id"])
                embedded_data_points_agent(case_id, token_data["token"])
                return api_response([], "Patient record updated", "success")
            else:
                raise HTTPException(status.HTTP_404_NOT_FOUND, "patient not found")
        else:
            return api_response([], "Nothing to update", "success")

    except Exception:
        raise


@router.put("/patient/{case_id}/chadscore", response_model=ResponseSchema)
async def update_patient_chadscore(
    request: Request,
    response: Response,
    body: ChadscoreSchema,
    case_id: str,
    token_data: dict = Depends(authorizer),
):
    try:
        filtered_data = body.model_dump(exclude_defaults=True, exclude_unset=True)
        mongo_client: Database = request.app.database
        chad_collection = mongo_client.get_collection("laao_cha2ds2_vasc_risk_score")
        if filtered_data:
            res = chad_collection.update_one(
                {"case_id.$id": ObjectId(case_id)}, {"$set": filtered_data}
            )
            if res.matched_count == 0:
                raise HTTPException(status.HTTP_404_NOT_FOUND, "data not found")
            embedded_data_points_agent(case_id, token_data["token"])
            return api_response([], "record updated", "success")
        else:
            return api_response([], "Nothing to update", "success")
    except Exception:
        raise


@router.put("/patient/{case_id}/hasbled", response_model=ResponseSchema)
async def update_patient_hasbled(
    request: Request,
    response: Response,
    body: HasbledScoreSchema,
    case_id: str,
    token_data: dict = Depends(authorizer),
):
    try:
        filtered_data = body.model_dump(exclude_defaults=True, exclude_unset=True)
        mongo_client: Database = request.app.database
        hasbled_collection = mongo_client.get_collection("laao_has_bled_risk_score")
        if filtered_data:
            res = hasbled_collection.update_one(
                {"case_id.$id": ObjectId(case_id)}, {"$set": filtered_data}
            )
            if res.matched_count == 0:
                raise HTTPException(status.HTTP_404_NOT_FOUND, "data not found")
            embedded_data_points_agent(case_id, token_data["token"])
            return api_response([], "record updated", "success")
        else:
            return api_response([], "Nothing to update", "success")
    except Exception:
        raise
