import pandas as pd
import xml.etree.ElementTree as ET
import uuid
from xml.dom import minidom
import os

# Load section structure
section_csv = r"C:\Users\<USER>\Desktop\cm-api\app\v2\data_sources\ncdr\LAAO v1.4 Data Dictionary Full Specifications.csv"
section_df = pd.read_csv(section_csv)
section_df = section_df.dropna(subset=['Parent Section'])

# Load elements CSV
elements_csv = r"C:\Users\<USER>\Desktop\cm-api\app\v2\data_sources\ncdr\elements.csv"
elements_df = pd.read_csv(elements_csv)

#Load section csv
selections_csv = r"C:\Users\<USER>\Desktop\cm-api\app\v2\data_sources\ncdr\selections.csv"
selections_df = pd.read_csv(selections_csv)
# Ensure xsi namespace is registered
ET.register_namespace("xsi", "http://www.w3.org/2001/XMLSchema-instance")

def add_elements_to_section(section_el, section_code):
    """Add all <element> tags for a given section."""
    section_elements = elements_df[elements_df['Section Code'] == section_code]
    for _, row in section_elements.iterrows():
        code = str(row['Code'])
        code_system = str(row['Code System'])
        display_name = row['Name']
        data_type = str(row['Data Type']).strip() if pd.notna(row['Data Type']) else "ST"
        element_ref = str(row['Element Reference']).strip()

        element_el = ET.SubElement(section_el, "element", {
            "code": code,
            "codeSystem": code_system,
            "displayName": display_name
        })

        if data_type == "CD":
            sel_rows = selections_df[selections_df['Element Reference'].astype(str) == element_ref]
            for _, sel in sel_rows.iterrows():
                ET.SubElement(element_el, "value", {
                    "{http://www.w3.org/2001/XMLSchema-instance}type": "CD",
                    "code": str(sel['Code']),
                    "codeSystem": str(sel['Code System']),
                    "displayName": str(sel['Selection Name'])
                })
        else:
            ET.SubElement(element_el, "value", {
                "{http://www.w3.org/2001/XMLSchema-instance}type": data_type,
                "value": ""
            })

def build_sections(parent_element, parent_section_name, container_df, visited=None):
    if visited is None:
        visited = set()

    if parent_section_name in visited:
        return
    visited.add(parent_section_name)

    children = container_df[container_df['Parent Section'] == parent_section_name]

    for _, row in children.iterrows():
        section_code = str(row['Section Code'])
        section_name = row['Section Display Name']

        if section_code == "PREPROCMED":
            section_elements = elements_df[elements_df['Section Code'] == section_code]

            med_row = section_elements[section_elements['Name'].str.contains("Pre-procedure Medication Code", na=False)].iloc[0]
            med_ref = str(med_row['Element Reference']).strip()
            med_values = selections_df[selections_df['Element Reference'].astype(str) == med_ref]

            admin_row = section_elements[section_elements['Name'].str.contains("Medication Administered", na=False)].iloc[0]
            admin_ref = str(admin_row['Element Reference']).strip()
            admin_values = selections_df[selections_df['Element Reference'].astype(str) == admin_ref]

            for _, med in med_values.iterrows():
                new_section = ET.SubElement(parent_element, "section", {
                    "code": section_code,
                    "displayName": section_name
                })

                # Medication element
                med_el = ET.SubElement(new_section, "element", {
                    "code": str(med_row['Code']),
                    "codeSystem": str(med_row['Code System']),
                    "displayName": "Medication"
                })

                ET.SubElement(med_el, "value", {
                    "{http://www.w3.org/2001/XMLSchema-instance}type": "CD",
                    "code": str(med['Code']),
                    "codeSystem": str(med['Code System']),
                    "displayName": str(med['Selection Name'])
                })

                # Medication Administered element
                admin_el = ET.SubElement(new_section, "element", {
                    "code": str(admin_row['Code']),
                    "codeSystem": str(admin_row['Code System']),
                    "displayName": "Pre-Procedure Medication Administered"
                })

                for _, admin_val in admin_values.iterrows():
                    ET.SubElement(admin_el, "value", {
                        "{http://www.w3.org/2001/XMLSchema-instance}type": "CD",
                        "code": str(admin_val['Code']),
                        "codeSystem": str(admin_val['Code System']),
                        "displayName": str(admin_val['Selection Name'])
                    })
            continue  # Skip regular processing for PREPROCMED
        

        # if section_code == "IPPEVENTS":
        #     # Only generate IPPEVENTS if we're inside a PROC section
        #     if current_procinfo_section is not None:
        #         section_elements = elements_df[elements_df['Section Code'] == section_code]
        #         event_row = section_elements[section_elements['Name'].str.contains("Intra or Post Procedure Events", na=False)].iloc[0]
        #         event_ref = str(event_row['Element Reference']).strip()
        #         event_values = selections_df[selections_df['Element Reference'].astype(str) == event_ref]

        #         occurred_row = section_elements[section_elements['Name'].str.contains("Event Occurred", na=False)].iloc[0]

        #         for _, event in event_values.iterrows():
        #             new_section = ET.SubElement(current_procinfo_section, "section", {
        #                 "code": section_code,
        #                 "displayName": section_name
        #             })

        #             event_el = ET.SubElement(new_section, "element", {
        #                 "code": str(event_row['Code']),
        #                 "codeSystem": str(event_row['Code System']),
        #                 "displayName": event_row['Name']
        #             })

        #             ET.SubElement(event_el, "value", {
        #                 "{http://www.w3.org/2001/XMLSchema-instance}type": "CD",
        #                 "code": str(event['Code']),
        #                 "codeSystem": str(event['Code System']),
        #                 "displayName": str(event['Selection Name'])
        #             })

        #             occurred_el = ET.SubElement(new_section, "element", {
        #                 "code": str(occurred_row['Code']),
        #                 "codeSystem": str(occurred_row['Code System']),
        #                 "displayName": occurred_row['Name']
        #             })

        #             ET.SubElement(occurred_el, "value", {
        #                 "{http://www.w3.org/2001/XMLSchema-instance}type": "BL",
        #                 "value": " "
        #             })
        #             pass
        #     continue

        section_el = ET.SubElement(parent_element, "section", {
            "code": section_code,
            "displayName": section_name
        })

        # new_procinfo = section_el if section_code == "PROCINFO" else current_procinfo_section
        add_elements_to_section(section_el, section_code)
        build_sections(section_el, section_name, container_df, visited.copy())

# Build XML structure
registry = ET.Element("registryDocument", {
    "xmlns:xsd": "http://www.w3.org/2001/XMLSchema",
    "schemaVersion": "1.0"
})

# Patient section
patient = ET.SubElement(registry, "patient", {"ncdrPatientId": "8145894"})
patient_container_df = section_df[section_df['Container Class'] == "patientContainer"]
build_sections(patient, "Root", patient_container_df)

# Episode section
episode_key = str(uuid.uuid4())
episode = ET.SubElement(patient, "episode", {"episodeKey": episode_key})
episode_container_df = section_df[section_df['Container Class'] == "episodeContainer"]
build_sections(episode, "Root", episode_container_df)

# Output pretty XML
def prettify_xml(elem):
    rough_string = ET.tostring(elem, 'utf-8')
    return minidom.parseString(rough_string).toprettyxml(indent="  ")

output_folder= os.path.abspath("v2/data_sources/ncdr")
os.makedirs(output_folder, exist_ok=True)
output_path = os.path.join(output_folder,"generated_registry_documenttwo.xml")
with open(output_path, "w", encoding="utf-8") as f:
    f.write(prettify_xml(registry))

print(f"XML with elements generated: {output_path}")
