from v2.utils import authorizer, api_response, embedded_data_points_agent
from pymongo.database import Database, DBRef
from fastapi import APIRouter, Request, Response, Depends, status, HTTPException
from v2.schema import (
    ResponseSchema,
    ListofPatients,
    User,
    ListofPatientsQueryParams,
    AbstractorPatientData,
    AbstractorTemplate,
)
from v2.constants import ABSTR<PERSON><PERSON>R<PERSON>LE, ABSTRACTOR_TASK, ABSTRACTOR_TASK_KEYS
from datetime import datetime, timedelta
from bson import ObjectId

router = APIRouter(prefix="/abstractor", tags=["V2 Abstractor"])


@router.get("/patients", response_model=ResponseSchema[ListofPatients])
async def get_list_of_all_patients(
    request: Request,
    response: Response,
    queryparam: ListofPatientsQueryParams = Depends(),
    token_data: dict = Depends(authorizer),
):
    try:
        user_data = User(**token_data.get("data", {}))
        mongo_client: Database = request.app.database
        collection = mongo_client.get_collection("case_summary")

        user_collection = mongo_client.get_collection("user")
        user_db_data = user_collection.find_one({"keycloak_id": user_data.sub})

        start_date = (
            datetime.strptime(queryparam.start_date, "%Y-%m-%d") - timedelta(days=1)
        ).strftime("%Y-%m-%d")
        end_date = (
            datetime.strptime(queryparam.end_date, "%Y-%m-%d") - timedelta(days=1)
        ).strftime("%Y-%m-%d")

        if user_db_data.get("role") == ABSTRACTORROLE:
            data = collection.aggregate(
                [
                    {
                        "$match": {
                            "deleted_at": None,
                            "procedure_date": {
                                "$gte": start_date,
                                "$lte": end_date,
                            },
                        }
                    },
                    {
                        "$lookup": {
                            "from": "laao_procedure_details",
                            "localField": "_id",
                            "foreignField": "case_id.$id",
                            "as": "laao_procedure_details",
                        }
                    },
                    {
                        "$unwind": {
                            "path": "$laao_procedure_details",
                            "preserveNullAndEmptyArrays": True,
                        }
                    },
                    {
                        "$match": {
                            "laao_procedure_details.deleted_at": None,
                            # "laao_procedure_details.device_deployed": True,
                        }
                    },
                    # {
                    #     "$lookup": {
                    #         "from": "post_op_details",
                    #         "localField": "_id",
                    #         "foreignField": "case_id.$id",
                    #         "as": "post_op_details",
                    #     }
                    # },
                    # {
                    #     "$unwind": {
                    #         "path": "$post_op_details",
                    #         "preserveNullAndEmptyArrays": True,
                    #     }
                    # },
                    # {
                    #     "$match": {
                    #         "post_op_details.follow_ups_1_yr.completed": True,
                    #         "post_op_details.deleted_at": None,
                    #     }
                    # },
                    {
                        "$lookup": {
                            "from": "user_site_mapping",
                            "localField": "site_id.$id",
                            "foreignField": "site_id.$id",
                            "as": "user_site_map_details",
                        }
                    },
                    {
                        "$unwind": {
                            "path": "$user_site_map_details",
                            "preserveNullAndEmptyArrays": True,
                        }
                    },
                    {
                        "$lookup": {
                            "from": "user",
                            "localField": "user_site_map_details.user_id.$id",
                            "foreignField": "_id",
                            "as": "user_details",
                        }
                    },
                    {
                        "$unwind": {
                            "path": "$user_details",
                            "preserveNullAndEmptyArrays": True,
                        }
                    },
                    {
                        "$match": {
                            "user_details.keycloak_id": user_data.sub,
                            "user_details.deleted_at": None,
                        }
                    },
                    {
                        "$lookup": {
                            "from": "patient",
                            "localField": "patient_id.$id",
                            "foreignField": "_id",
                            "as": "patient_details",
                        }
                    },
                    {
                        "$unwind": {
                            "path": "$patient_details",
                            "preserveNullAndEmptyArrays": True,
                        }
                    },
                    {
                        "$match": {
                            "patient_details.deleted_at": None,
                        }
                    },
                    {
                        "$lookup": {
                            "from": "referring_provider",
                            "localField": "patient_details.referring_providers.$id",
                            "foreignField": "_id",
                            "as": "referring_provider_details",
                        }
                    },
                    {
                        "$lookup": {
                            "from": "referring_provider_credentials",
                            "localField": "referring_provider_details.credential.$id",
                            "foreignField": "_id",
                            "as": "credential_info",
                        }
                    },
                    {
                        "$unwind": {
                            "path": "$referring_provider_details",
                            "preserveNullAndEmptyArrays": True,
                        }
                    },
                    {"$match": {"referring_provider_details.deleted_at": None}},
                    {"$sort": {"procedure_date": 1, "procedure_time": 1}},
                    {
                        "$project": {
                            "_id": 0,
                            "case_id": "$_id",
                            "procedure_date": 1,
                            "patient_id": "$patient_details._id",
                            "patient_name": {
                                "$concat": [
                                    {
                                        "$ifNull": [
                                            "$patient_details.first_name",
                                            "",
                                        ]
                                    },
                                    " ",
                                    {"$ifNull": ["$patient_details.last_name", ""]},
                                ]
                            },
                            "dob": "$patient_details.dob",
                            "referring_provider": {
                                "$concat": [
                                    {
                                        "$ifNull": [
                                            "$referring_provider_details.first_name",
                                            None,
                                        ]
                                    },
                                    " ",
                                    {
                                        "$ifNull": [
                                            "$referring_provider_details.last_name",
                                            None,
                                        ]
                                    },
                                    ", ",
                                    {
                                        "$ifNull": [
                                            {
                                                "$arrayElemAt": [
                                                    "$credential_info.name",
                                                    0,
                                                ]
                                            },
                                            "",
                                        ]
                                    },
                                ]
                            },
                        }
                    },
                ]
            ).to_list()
        else:
            raise HTTPException(
                status.HTTP_403_FORBIDDEN, "you don't have access to this route"
            )

        response.status_code = status.HTTP_200_OK

        if data:
            return api_response(data, "Patients list fetched", "success")
        else:
            return api_response([], "No records found", "success")
    except Exception:
        raise


@router.get("/patients/{case_id}", response_model=ResponseSchema[AbstractorTemplate])
async def get_specific_patient_details(
    request: Request,
    response: Response,
    case_id: str,
    token_data: dict = Depends(authorizer),
):
    try:
        mongo_client: Database = request.app.database
        collection = mongo_client.get_collection("patient_registry_details")

        data = collection.aggregate(
            [
                {
                    "$match": {
                        "deleted_at": None,
                        "case_id.$id": ObjectId(case_id),
                    }
                },
            ]
        ).to_list()
        response.status_code = status.HTTP_200_OK

        if data:
            return api_response(data, "Patient detail fetched", "success")
        else:
            return api_response([], "No records found", "success")
    except Exception:
        raise


@router.put("/patients/{case_id}", response_model=ResponseSchema)
async def update_specific_patient_details(
    request: Request,
    case_id: str,
    body: dict,
    token_data: dict = Depends(authorizer),
):
    try:
        user_data = User(**token_data.get("data", {}))
        mongo_client: Database = request.app.database

        user_collection = mongo_client.get_collection("user")
        user_db_data = user_collection.find_one({"keycloak_id": user_data.sub})

        collection = mongo_client.get_collection("patient_registry_details")
        case_summary_collection = mongo_client.get_collection("case_summary")
        task_sub_type_collection = mongo_client.get_collection("task_sub_type")
        task_details_collection = mongo_client.get_collection("task_details")

        body.update({"updated_at": datetime.now().isoformat()})

        # case_id_str = body.pop("case_id")

        # body['case_id'] = DBRef('case_summary',ObjectId(case_id_str))

        result = collection.update_one(
            {"case_id.$id": ObjectId(case_id), "deleted_at": None},
            {"$set": body},
        )

        task_check_keys = ABSTRACTOR_TASK_KEYS

        task_key_list = []

        # Traverse JSON and classify values
        for key in task_check_keys:
            sections = key.split(".")
            original_ref = body
            for part in sections[:-1]:  # Traverse through nested keys
                original_ref = original_ref.get(part, {})

            value = (
                original_ref.get(sections[-1], "").strip().lower()
            )  # Handle case where value is empty

            if value == "yes" or (value != "" and value != "no"):
                cleaned_key = key.replace(".value", "")
                task_key_list.append(cleaned_key)

        task_sub_type_id = list(
            task_sub_type_collection.aggregate(
                [
                    {"$match": {"name": ABSTRACTOR_TASK}},
                    {
                        "$project": {
                            "_id": 1,
                        }
                    },
                ]
            )
        )

        existing_task = task_details_collection.find_one(
            {
                "case_id.$id": ObjectId(case_id),
                "task_sub_type_id.$id": ObjectId(task_sub_type_id[0]["_id"]),
                "deleted_at": None,
            }
        )

        if existing_task:
            task_details_collection.update_one(
                {
                    "case_id": DBRef("case_summary", ObjectId(case_id)),
                    "deleted_at": None,
                },
                {
                    "$set": {
                        "task_key": task_key_list,
                        "updated_at": datetime.now().isoformat(),
                    }
                },
            )
        else:
            task_details_collection.insert_one(
                {
                    "assigned_to": DBRef("user", ObjectId(user_db_data["_id"])),
                    "assigned_by": DBRef("user", ObjectId(user_db_data["_id"])),
                    "task_sub_type_id": DBRef(
                        "task_sub_type", ObjectId(task_sub_type_id[0]["_id"])
                    ),
                    "case_id": DBRef("case_summary", ObjectId(case_id)),
                    "due_date": (datetime.now() + timedelta(weeks=1)).strftime(
                        "%Y-%m-%d"
                    ),
                    "due_time": datetime.now().strftime("%H:%M:%S"),
                    "task_status": "PENDING",
                    "assigned_date": datetime.now().strftime("%Y-%m-%d"),
                    "assigned_time": datetime.now().strftime("%H:%M:%S"),
                    "auto_complete": False,
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat(),
                    "deleted_at": None,
                    "task_key": task_key_list,
                }
            )
        if result.matched_count > 0:
            embedded_data_points_agent(case_id, token_data["token"])
            return api_response(
                [], "Patient details updated and task created", "success"
            )
        else:
            raise HTTPException(status.HTTP_404_NOT_FOUND, "Patient details not found")

    except Exception:
        raise


@router.get("/template", response_model=ResponseSchema[AbstractorTemplate])
async def get_specific_patient_details(
    request: Request,
    response: Response,
    token_data: dict = Depends(authorizer),
):
    try:
        mongo_client: Database = request.app.database
        collection = mongo_client.get_collection("laao_abstractor_template")

        data = collection.find_one({}, {"_id": 0})  # Excludes _id

        response.status_code = status.HTTP_200_OK

        if data:
            return api_response(
                data.get("template", {}), "Template detail fetched", "success"
            )
        else:
            return api_response([], "No records found", "success")

    except Exception:
        raise
