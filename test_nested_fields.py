#!/usr/bin/env python3
"""
Test script to verify nested field extraction and XML processing for NCDR
"""

import sys
import os
import tempfile
import xml.etree.ElementTree as ET
sys.path.append(os.path.abspath('.'))

from app.v2.utils.ncdr_registry_utils import extract_fields_with_label, process_ncdr_data

# Test data with nested if_yes structure
test_data = {
    "left_atrial_appendage_occlusion_intervention": {
        "field_id": "14804",
        "label": "Left Atrial Appendage Occlusion Intervention",
        "options": ["No", "Yes"],
        "input_type": "multi_input_field",
        "if_yes": {
            "left_atrial_appendage_intervention_type": {
                "field_id": "14806",
                "label": "Left Atrial Appendage Intervention Type",
                "options": [
                    {"id": "1", "value": "Epicardial Ligation"},
                    {"id": "2", "value": "Surgical Amputation"},
                    {"id": "3", "value": "Surgical Ligation"},
                    {"id": "4", "value": "Percutaneous Occlusion"},
                    {"id": "5", "value": "Surgical Closure Device"},
                    {"id": "6", "value": "Surgical Stapling"}
                ],
                "input_type": "multi_select",
                "value": ["Surgical Amputation"],
                "modified_by": "ABSTRACTOR",
                "value_source": "Embeddings"
            }
        },
        "value": "Yes",
        "modified_by": "ABSTRACTOR",
        "value_source": "Embeddings"
    }
}

def test_extraction():
    print("Testing nested field extraction...")

    # Extract fields
    extracted_fields = extract_fields_with_label(test_data)

    print(f"\nExtracted {len(extracted_fields)} fields:")
    for field in extracted_fields:
        print(f"  Field ID: {field['field_id']}, Value: {field['value']}, Label: {field['label']}")

    # Check if both parent and nested fields are extracted
    field_ids = [f['field_id'] for f in extracted_fields]

    expected_fields = ['14804', '14806']
    missing_fields = [fid for fid in expected_fields if fid not in field_ids]

    if missing_fields:
        print(f"\n❌ FAILED: Missing field IDs: {missing_fields}")
        return False
    else:
        print(f"\n✅ SUCCESS: All expected fields extracted")

        # Check values
        field_14804 = next((f for f in extracted_fields if f['field_id'] == '14804'), None)
        field_14806 = next((f for f in extracted_fields if f['field_id'] == '14806'), None)

        if field_14804 and field_14804['value'] == 'Yes':
            print(f"✅ Field 14804 has correct value: {field_14804['value']}")
        else:
            print(f"❌ Field 14804 has incorrect value: {field_14804['value'] if field_14804 else 'None'}")

        if field_14806 and field_14806['value'] == ['Surgical Amputation']:
            print(f"✅ Field 14806 has correct value: {field_14806['value']}")
        else:
            print(f"❌ Field 14806 has incorrect value: {field_14806['value'] if field_14806 else 'None'}")

        return True

def test_xml_processing():
    print("\n" + "="*50)
    print("Testing XML processing...")

    # Extract fields
    extracted_fields = extract_fields_with_label(test_data)

    # Create temporary output file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.xml', delete=False) as tmp_file:
        tmp_path = tmp_file.name

    try:
        # Process the data
        process_ncdr_data(extracted_fields, tmp_path)

        # Read and parse the generated XML
        tree = ET.parse(tmp_path)
        root = tree.getroot()

        # Find the Left Atrial Appendage Occlusion Intervention element
        laa_intervention_element = None
        laa_type_element = None

        for element in root.findall(".//element"):
            display_name = element.get("displayName", "")
            if display_name == "Left Atrial Appendage Occlusion Intervention":
                laa_intervention_element = element
            elif display_name == "Left Atrial Appendage Intervention Type":
                laa_type_element = element

        # Check the BL value for field 14804
        if laa_intervention_element is not None:
            value_element = laa_intervention_element.find("value")
            if value_element is not None:
                value_attr = value_element.get("value")
                xsi_type = value_element.get("{http://www.w3.org/2001/XMLSchema-instance}type")

                print(f"✅ Found Left Atrial Appendage Occlusion Intervention element")
                print(f"   Type: {xsi_type}, Value: '{value_attr}'")

                if xsi_type == "BL" and value_attr == "true":
                    print(f"✅ Field 14804 correctly set to 'true' for BL type")
                elif xsi_type == "BL" and value_attr == "":
                    print(f"❌ Field 14804 has empty value - should be 'true'")
                else:
                    print(f"❌ Field 14804 has unexpected value: {value_attr}")
            else:
                print(f"❌ No value element found for Left Atrial Appendage Occlusion Intervention")
        else:
            print(f"❌ Left Atrial Appendage Occlusion Intervention element not found")

        # Check the CD value for field 14806
        if laa_type_element is not None:
            print(f"✅ Found Left Atrial Appendage Intervention Type element")
            value_elements = laa_type_element.findall("value")
            print(f"   Found {len(value_elements)} value elements")

            for value_element in value_elements:
                display_name = value_element.get("displayName", "")
                code = value_element.get("code", "")
                print(f"   Value: {display_name} (code: {code})")
        else:
            print(f"❌ Left Atrial Appendage Intervention Type element not found")

        return True

    except Exception as e:
        print(f"❌ Error processing XML: {e}")
        return False
    finally:
        # Clean up
        try:
            os.unlink(tmp_path)
        except:
            pass

if __name__ == "__main__":
    success1 = test_extraction()
    success2 = test_xml_processing()

    if success1 and success2:
        print(f"\n🎉 All tests passed!")
    else:
        print(f"\n💥 Some tests failed!")
