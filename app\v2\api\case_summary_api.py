from fastapi import APIRouter, Response, Request, Depends, status, HTTPException
from pymongo.database import Database, DBRef
from bson import ObjectId
from v2.utils import (
    api_response,
    get_cha2ds2_vasc_score,
    embedded_data_points_agent,
    get_patient_data,
)
from v2.schema import (
    CaseSummarySchema,
    ResponseSchema,
    UpdatePatientCaseSchema,
    ResponseDictSchema,
    User,
)
from v2.utils import authorizer, api_response, update_follow_up_tasks
from v2.constants import FOLLOW_UP_45_DAYS, FOLLOW_UP_6_MONTHS, COORDROLE
from datetime import datetime

router = APIRouter(prefix="/cases", tags=["V2 Case Summary"])


@router.get("/{case_id}", response_model=ResponseDictSchema[CaseSummarySchema])
async def get_specific_case(
    request: Request,
    response: Response,
    case_id: str,
    token_data: dict = Depends(authorizer),
):
    try:
        mongo_client: Database = request.app.database
        collection = mongo_client.get_collection("case_summary")

        data = collection.aggregate(
            pipeline=[
                {"$match": {"_id": ObjectId(case_id), "deleted_at": None}},
                {
                    "$lookup": {
                        "from": "patient",
                        "localField": "patient_id.$id",
                        "foreignField": "_id",
                        "as": "patient_data",
                    }
                },
                {
                    "$lookup": {
                        "from": "referring_provider",
                        "localField": "patient_data.pcp.$id",
                        "foreignField": "_id",
                        "as": "pcp_data",
                    }
                },
                {
                    "$lookup": {
                        "from": "referring_provider",
                        "localField": "patient_data.referring_providers.$id",
                        "foreignField": "_id",
                        "as": "ref_provider_data",
                    }
                },
                {
                    "$lookup": {
                        "from": "referring_provider_credentials",
                        "localField": "ref_provider_data.credential.$id",
                        "foreignField": "_id",
                        "as": "credential_info",
                    }
                },
                # {
                #     "$project": {
                #         "credential": 1,
                #         "credential_info": 1,
                #     }
                # },
                
                
                {
                    "$unwind": {
                        "path": "$patient_data",
                        "preserveNullAndEmptyArrays": True,
                    }
                },
                {
                    "$lookup": {
                        "from": "site",
                        "localField": "site_id.$id",
                        "foreignField": "_id",
                        "as": "site_data",
                    }
                },
                {"$unwind": {"path": "$site_data", "preserveNullAndEmptyArrays": True}},
                {
                    "$lookup": {
                        "from": "implanting_physicians",
                        "localField": "implanting_physician_id.$id",
                        "foreignField": "_id",
                        "as": "implanting_physician_data",
                    }
                },
                {
                    "$unwind": {
                        "path": "$implanting_physician_data",
                        "preserveNullAndEmptyArrays": True,
                    }
                },
                {
                    "$lookup": {
                        "from": "user",
                        "localField": "rep_id.$id",
                        "foreignField": "_id",
                        "as": "rep_data",
                    }
                },
                {"$unwind": {"path": "$rep_data", "preserveNullAndEmptyArrays": True}},
                {
                    "$lookup": {
                        "from": "laao_procedure_details",
                        "localField": "_id",
                        "foreignField": "case_id.$id",
                        "as": "laao_procedure_details_data",
                    }
                },
                {
                    "$unwind": {
                        "path": "$laao_procedure_details_data",
                        "preserveNullAndEmptyArrays": True,
                    }
                },
                {
                    "$lookup": {
                        "from": "laao_string_selections",
                        "localField": "laao_procedure_details_data.complication_id.$id",
                        "foreignField": "_id",
                        "as": "complication_string",
                    }
                },
                {
                    "$unwind": {
                        "path": "$complication_string",
                        "preserveNullAndEmptyArrays": True,
                    }
                },
                {
                    "$lookup": {
                        "from": "laao_string_selections",
                        "localField": "laao_procedure_details_data.tsp_location.$id",
                        "foreignField": "_id",
                        "as": "laao_string_selections_data",
                    }
                },
                {
                    "$unwind": {
                        "path": "$laao_string_selections_data",
                        "preserveNullAndEmptyArrays": True,
                    }
                },
                {
                    "$lookup": {
                        "from": "laao_string_selections",
                        "localField": "laao_procedure_details_data.laao_morphology.$id",
                        "foreignField": "_id",
                        "as": "morphology_data",
                    }
                },
                {
                    "$unwind": {
                        "path": "$morphology_data",
                        "preserveNullAndEmptyArrays": True,
                    }
                },
                {
                    "$lookup": {
                        "from": "device_type",
                        "localField": "laao_procedure_details_data.device_type.$id",
                        "foreignField": "_id",
                        "as": "device_name",
                    }
                },
                {
                    "$unwind": {
                        "path": "$device_name",
                        "preserveNullAndEmptyArrays": True,
                    }
                },
                {"$match": {"device_name.deleted_at": None}},
                {
                    "$lookup": {
                        "from": "post_op_details",
                        "let": {"case_id": "$_id"},
                        "pipeline": [
                            {
                                "$match": {
                                    "$expr": {"$eq": ["$case_id.$id", "$$case_id"]},
                                    "deleted_at": None,
                                }
                            },
                            {
                                "$unwind": {
                                    "path": "$anticoagulation",
                                    "preserveNullAndEmptyArrays": True,
                                }
                            },
                            {
                                "$lookup": {
                                    "from": "medicines",
                                    "localField": "anticoagulation.medicine.$id",
                                    "foreignField": "_id",
                                    "as": "medicine_details",
                                }
                            },
                            {
                                "$unwind": {
                                    "path": "$medicine_details",
                                    "preserveNullAndEmptyArrays": True,
                                }
                            },
                            {
                                "$match": {
                                    "$expr": {
                                        "$eq": ["$medicine_details.deleted_at", None]
                                    }
                                }
                            },
                            {
                                "$lookup": {
                                    "from": "laao_string_selections",
                                    "localField": "anticoagulation.dosing_frequency.$id",
                                    "foreignField": "_id",
                                    "as": "frequency_details",
                                }
                            },
                            {
                                "$unwind": {
                                    "path": "$frequency_details",
                                    "preserveNullAndEmptyArrays": True,
                                }
                            },
                            {
                                "$match": {
                                    "$expr": {
                                        "$eq": ["$frequency_details.deleted_at", None]
                                    }
                                }
                            },
                            {
                                "$lookup": {
                                    "from": "laao_string_selections",
                                    "localField": "anticoagulation.period.duration.$id",
                                    "foreignField": "_id",
                                    "as": "duration_details",
                                }
                            },
                            {
                                "$unwind": {
                                    "path": "$duration_details",
                                    "preserveNullAndEmptyArrays": True,
                                }
                            },
                            {
                                "$match": {
                                    "$expr": {
                                        "$eq": ["$duration_details.deleted_at", None]
                                    }
                                }
                            },
                            {
                                "$project": {
                                    "formatted_medicine": {
                                        "$concat": [
                                            "$medicine_details.name",
                                            " - ",
                                            {"$toString": "$anticoagulation.quantity"},
                                            " ",
                                            "$frequency_details.name",
                                            " x ",
                                            {
                                                "$toString": {
                                                    "$ifNull": [
                                                        "$anticoagulation.period.count",
                                                        0,
                                                    ]
                                                }
                                            },
                                            " ",
                                            "$duration_details.name",
                                        ]
                                    }
                                }
                            },
                            {
                                "$group": {
                                    "_id": "$$case_id",
                                    "name": {"$addToSet": "$formatted_medicine"},
                                }
                            },
                        ],
                        "as": "medicines_selected",
                    }
                },
                {
                    "$lookup": {
                        "from": "laao_string_selections",
                        "localField": "rationale.$id",
                        "foreignField": "_id",
                        "as": "rationale_info",
                    }
                },
                {
                    "$unwind": {
                        "path": "$rationale_info",
                        "preserveNullAndEmptyArrays": True,
                    }
                },
                {
                    "$match": {
                        "rationale_info.deleted_at": None,
                    }
                },
                {
                    "$project": {
                        "patient_id": "$patient_data._id",
                        "patient": {
                            "name": {
                                "$concat": [
                                    "$patient_data.first_name",
                                    " ",
                                    {"$ifNull": ["$patient_data.middle_name", ""]},
                                    " ",
                                    "$patient_data.last_name",
                                ]
                            },
                            "rationale": {"$ifNull": ["$rationale_info.name", ""]},
                            "dob": {"$ifNull": ["$patient_data.dob", ""]},
                            "ssn": {"$ifNull": ["$patient_data.ssn", ""]},
                            "phone_number": {
                                "$ifNull": ["$patient_data.phone_number", ""]
                            },
                            "email_id": {"$ifNull": ["$patient_data.email_id", ""]},
                            "address": {"$ifNull": ["$patient_data.address", ""]},
                            "city": {"$ifNull": ["$patient_data.city", ""]},
                            "state": {"$ifNull": ["$patient_data.state", ""]},
                            "zip_code": {"$ifNull": ["$patient_data.zip_code", ""]},
                            "pcp": {
                                "name": {
                                    "$concat": [
                                        {"$first": "$pcp_data.first_name"},
                                        " ",
                                        {"$first": "$pcp_data.last_name"},
                                        ", ",
                                        {
                                            "$ifNull": [
                                                {"$first": "$credential_info.name"},
                                                "",
                                            ]
                                        },
                                    ]
                                }
                            },
                            "referring_provider": {
                                "name": {
                                    "$concat": [
                                        {"$first": "$ref_provider_data.first_name"},
                                        " ",
                                        {"$first": "$ref_provider_data.last_name"},
                                        ", ",
                                        {
                                            "$ifNull": [
                                                {
                                                    "$first": "$credential_info.name"
                                                },
                                                "",
                                            ]
                                        },
                                    ]
                                }
                            },
                        },
                        "site": {
                            "name": {"$ifNull": ["$site_data.name", ""]},
                            "address": {"$ifNull": ["$site_data.address", ""]},
                            "city": {"$ifNull": ["$site_data.city", ""]},
                            "state": {"$ifNull": ["$site_data.state", ""]},
                            "zip_code": {"$ifNull": ["$site_data.zip_code", ""]},
                        },
                        "implanting_physician": {
                            "name": {
                                "$concat": [
                                    "$implanting_physician_data.first_name",
                                    " ",
                                    "$implanting_physician_data.last_name",
                                ]
                            },
                            "email_id": {
                                "$ifNull": ["$implanting_physician_data.email_id", ""]
                            },
                            "phone_number": {
                                "$ifNull": [
                                    "$implanting_physician_data.phone_number",
                                    "",
                                ]
                            },
                            "address": {
                                "$ifNull": ["$implanting_physician_data.address", ""]
                            },
                        },
                        "rep": {
                            "name": {
                                "$concat": [
                                    "$rep_data.first_name",
                                    " ",
                                    "$rep_data.last_name",
                                ]
                            },
                            "email_id": {"$ifNull": ["$rep_data.email_id", ""]},
                        },
                        "case_details": {
                            "procedure_date": {"$ifNull": ["$procedure_date", ""]},
                            "device_name": {
                                "$ifNull": [
                                    "$device_name.name",
                                    "",
                                ]
                            },
                            "device_size": {
                                "$ifNull": [
                                    "$laao_procedure_details_data.device_size",
                                    0,
                                ]
                            },
                            "leak": {
                                "$ifNull": [
                                    "$laao_procedure_details_data.leak_value",
                                    0,
                                ]
                            },
                            "morphology": {
                                "$ifNull": [
                                    "$morphology_data.name",
                                    "",
                                ]
                            },
                            "tsp_location": {
                                "$ifNull": ["$laao_string_selections_data.name", ""]
                            },
                            "la_pressure": {
                                "$ifNull": [
                                    "$laao_procedure_details_data.la_pressure",
                                    0,
                                ]
                            },
                            "activated_clotting_time": {
                                "$ifNull": [
                                    "$laao_procedure_details_data.activated_clotting_time",
                                    0,
                                ]
                            },
                            "creatinine_value": {
                                "$ifNull": [
                                    "$laao_procedure_details_data.creatinine_value",
                                    0,
                                ]
                            },
                            "fluoro_time": {
                                "$ifNull": [
                                    "$laao_procedure_details_data.fluoro_time",
                                    0,
                                ]
                            },
                            "fluoro_total": {
                                "$ifNull": [
                                    "$laao_procedure_details_data.fluoro_total",
                                    0,
                                ]
                            },
                            "complication": {
                                "$cond": {
                                    "if": {
                                        "$eq": ["$complication_string.name", "Other"]
                                    },
                                    "then": "$laao_procedure_details_data.complication_other",
                                    "else": "$complication_string.name",
                                }
                            },
                            "baseline_tee_measurement": {
                                "$map": {
                                    "input": "$laao_procedure_details_data.tee_measurement",
                                    "as": "item",
                                    "in": {
                                        "angle": "$$item.angle",
                                        "width": "$$item.width",
                                        "depth": "$$item.depth",
                                    },
                                }
                            },
                            "final_measurement": {
                                "$map": {
                                    "input": "$laao_procedure_details_data.compression_ratio",
                                    "as": "item",
                                    "in": {
                                        "angle": "$$item.angle",
                                        "width": "$$item.value",
                                        "compression": "$$item.compression",
                                    },
                                }
                            },
                            "post_drug_rx": {
                                "$arrayElemAt": ["$medicines_selected.name", 0]
                            },
                        },
                    }
                },
            ]
        ).to_list()
        print(data)
        
        
        if not data:
            raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Case not found")
        else:
            data[0].get("patient", {}).update(
                {
                    "cha2ds2_vasc": get_cha2ds2_vasc_score(case_id, mongo_client).get(
                        "score", 0
                    )
                }
            )
        result = {
            "patient": data[0]["patient"],
            "site": data[0]["site"],
            "implanting_physician": data[0]["implanting_physician"],
            "rep": data[0]["rep"],
            "case_details": data[0]["case_details"],
        }

        response.status_code = status.HTTP_200_OK
        return api_response(result, "Case summary details fetched", "success")

    except Exception as er:
        raise


@router.put("/{case_id}", response_model=ResponseSchema)
async def update_patient_case(
    request: Request,
    response: Response,
    body: UpdatePatientCaseSchema,
    case_id: str,
    site_id: str,
    token_data: dict = Depends(authorizer),
):
    try:
        parsed_data = {k: v for k, v in body.model_dump().items() if v is not None}
        mongo_client: Database = request.app.database
        collection = mongo_client.get_collection("case_summary")

        # Get the User token
        user_data = User(**token_data.get("data", {}))
        keycloak_id = user_data.sub
        user_collection = mongo_client.get_collection("user")
        if COORDROLE in user_data.realm_access.get("roles", []):
            # Get the Site id
            data = list(
                user_collection.aggregate(
                    [
                        {"$match": {"deleted_at": None, "keycloak_id": keycloak_id}},
                        {
                            "$lookup": {
                                "from": "user_site_mapping",
                                "localField": "_id",
                                "foreignField": "user_id.$id",
                                "as": "user_site_mapping_details",
                            }
                        },
                        {"$unwind": "$user_site_mapping_details"},
                        {"$match": {"user_site_mapping_details.deleted_at": None}},
                        {
                            "$project": {
                                "_id": 1,
                                "email_id": 1,
                                "site_id": "$user_site_mapping_details.site_id",
                            }
                        },
                    ]
                )
            )
        if not (parsed_data):
            return api_response([], "Nothing to update", "success")

        implanting_physician_id = body.implanting_physician_id

        if implanting_physician_id:
            parsed_data.update(
                {
                    "implanting_physician_id": DBRef(
                        "implanting_physicians", ObjectId(implanting_physician_id)
                    )
                }
            )
        parsed_data["procedure_date"] = str(parsed_data["procedure_date"])
        updated_cases = collection.update_one(
            {
                "_id": ObjectId(case_id),
                "site_id.$id": ObjectId(site_id),
                "deleted_at": None,
            },
            {"$set": parsed_data},
        )
        parsed_data["procedure_date"] = datetime.strptime(
            parsed_data["procedure_date"], "%Y-%m-%d"
        ).date()

        if parsed_data["procedure_date"]:
            temp_task_data = {
                "assigner_id": data[0]["_id"],
                "assignee_id": data[0]["_id"],
                "case_id": case_id,
                "email_id": str(data[0]["email_id"]),
                "procedure_date": parsed_data["procedure_date"],
            }
            update_follow_up_tasks(
                mongo_client=mongo_client,
                task_sub_type=FOLLOW_UP_45_DAYS,
                task_data=temp_task_data,
            )
            update_follow_up_tasks(
                mongo_client=mongo_client,
                task_sub_type=FOLLOW_UP_6_MONTHS,
                task_data=temp_task_data,
            )

        if not (updated_cases.matched_count):
            raise HTTPException(
                status.HTTP_404_NOT_FOUND,
                detail=f"Can't find case ID {case_id} in site {site_id}",
            )
        # patient_data = get_patient_data(case_id, mongo_client, request.method)
        # patient_id = str(
        #     patient_data["patient_id"].id
        #     if hasattr(patient_data["patient_id"], "id")
        #     else patient_data["patient_id"]
        # )
        embedded_data_points_agent(case_id, token_data["token"])
        response.status_code = status.HTTP_200_OK
        return api_response(
            [{"no_of_records_changed": updated_cases.modified_count}],
            "Case updated successfully",
            "success",
        )
    except Exception:
        raise
