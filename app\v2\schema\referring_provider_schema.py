from pydantic import BaseModel, field_validator, ValidationInfo
from typing import Optional


class BaseReferringProvider(BaseModel):
    first_name: str
    last_name: str
    middle_name: Optional[str] = None
    credential: Optional[str] = (
        None  # Foreign key reference to referring_provider_credentials._id
    )
    npi_number: str  # REQUIRED
    email_id: Optional[str] = None
    phone_number: Optional[str] = None
    fax_number: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    hospital_system: Optional[str] = None


class ReferringProviderSchema(BaseReferringProvider):
    id: str

    @field_validator("id", mode="before")
    def convert_id(cls, v, values: ValidationInfo):
        return str(v)

    @field_validator("credential", mode="before")
    def convert_credential(cls, v, values: ValidationInfo):
        return str(v) if v else None


class RepReferringProviderSchema(BaseModel):
    id: str
    name: str

    @field_validator("id", mode="before")
    def convert_id(cls, v, values: ValidationInfo):
        return str(v)
