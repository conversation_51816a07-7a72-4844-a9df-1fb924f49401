from fastapi import APIRouter, Response, status, Request, Depends, HTTPException
from v2.utils import api_response, authorizer
from pymongo.database import Database
from v2.schema import ResponseSchema, RepPcpProviderSchema, PcpProviderSchema, BasePcpProvider
from bson import ObjectId, DBRef
from datetime import datetime
from typing import List

router = APIRouter(tags=["V2 Pcp Provider"], prefix="/pcp-providers")


@router.get('', response_model=ResponseSchema[RepPcpProviderSchema])
async def get_all_pcp_providers(
    request: Request, response: Response, token_data: dict = Depends(authorizer)
):
    try:
        mongo_client: Database = request.app.database
        rp_collection = mongo_client.get_collection("referring_provider")

        # Use aggregation to join with credentials collection using DBRef
        cursor = rp_collection.aggregate([
            {"$match": {"deleted_at": None, "specialty": "pcp"}},
            {
                "$lookup": {
                    "from": "referring_provider_credentials",
                    "localField": "credential.$id",
                    "foreignField": "_id",
                    "as": "credential_info"
                }
            },
            {
                "$project": {
                    "id": {"$toString": "$_id"},
                    "name": {
                        "$concat": [
                            "$first_name", " ", "$last_name", " ,",
                            { "$ifNull": [ { "$arrayElemAt": ["$credential_info.name", 0] }, "" ] }
                        ]
                    }
                }
            }
        ])
        data = list(cursor)

        response.status_code = status.HTTP_200_OK
        return api_response(data, "Pcp Provider details", "Success")
    except Exception:
        raise


@router.get("/credential-options", response_model=ResponseSchema[List[dict]])
async def get_credential_options(request: Request, token_data: dict = Depends(authorizer)):
    try:
        mongo_client: Database = request.app.database
        cred_collection = mongo_client.get_collection("referring_provider_credentials")
        cursor = cred_collection.find({"deleted_at": None}, {"_id": 1, "name": 1})
        data = list(cursor)

        for item in data:
            item["id"] = str(item["_id"])
            del item["_id"]

        return api_response(data, "Credential options fetched", "Success")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{id}", response_model=ResponseSchema[PcpProviderSchema])
async def get_pcp_provider_by_id(
    id: str,
    request: Request,
    response: Response,
    token_data: dict = Depends(authorizer),
):
    mongo_client: Database = request.app.database
    rp_collection = mongo_client.get_collection("referring_provider")

    try:
        # Use aggregation to join with credentials using DBRef
        cursor = rp_collection.aggregate([
            {"$match": {"_id": ObjectId(id), "deleted_at": None, "specialty": "pcp"}},
            {
                "$lookup": {
                    "from": "referring_provider_credentials",
                    "localField": "credential.$id",
                    "foreignField": "_id",
                    "as": "credential_info"
                }
            }
        ])
        
        provider_list = list(cursor)
        if not provider_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Pcp provider not found."
            )
        
        provider = provider_list[0]
        provider["id"] = str(provider["_id"])
        
        # Convert DBRef credential back to string ID for response
        if provider.get("credential") and hasattr(provider["credential"], "id"):
            provider["credential"] = str(provider["credential"].id)
        
        del provider["_id"]

        response.status_code = status.HTTP_200_OK
        return api_response(
            PcpProviderSchema(**provider),
            "Pcp provider details fetched",
            "Success"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post('', response_model=ResponseSchema[PcpProviderSchema])
async def create_pcp_provider(
    request: Request,
    provider_data: BasePcpProvider,
    response: Response,
    token_data: dict = Depends(authorizer),
):
    mongo_client: Database = request.app.database
    rp_collection = mongo_client.get_collection("referring_provider")
    cred_collection = mongo_client.get_collection("referring_provider_credentials")

    # Validate mandatory fields
    if not provider_data.first_name or not provider_data.last_name or not provider_data.npi_number:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="First name, last name, and NPI number are required."
        )

    # Validate credential if provided
    if provider_data.credential:
        try:
            credential = cred_collection.find_one({
                "_id": ObjectId(provider_data.credential),
                "deleted_at": None
            })
            if not credential:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid credential. Credential not found."
                )
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid credential format."
            )

    data = provider_data.dict()
    data["created_at"] = datetime.utcnow()
    data["deleted_at"] = None
    data["specialty"] = "pcp"  # Hard-coded specialty for PCP providers
    
    # Convert credential string to DBRef if provided
    if data.get("credential"):
        data["credential"] = DBRef("referring_provider_credentials", ObjectId(data["credential"]))

    result = rp_collection.insert_one(data)
    new_id = str(result.inserted_id)

    # Prepare response data with credential as string ID
    response_data = provider_data.dict()
    response_data["id"] = new_id

    response.status_code = status.HTTP_201_CREATED
    return api_response(
        PcpProviderSchema(**response_data),
        "Provider created",
        "Success",
    )


@router.put("/{id}", response_model=ResponseSchema[PcpProviderSchema])
async def update_pcp_provider(
    id: str,
    request: Request,
    update_data: BasePcpProvider,
    response: Response,
    token_data: dict = Depends(authorizer),
):
    mongo_client: Database = request.app.database
    rp_collection = mongo_client.get_collection("referring_provider")
    cred_collection = mongo_client.get_collection("referring_provider_credentials")

    existing = rp_collection.find_one({"_id": ObjectId(id), "deleted_at": None, "specialty": "pcp"})
    if not existing:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Provider not found.")

    # Validate credential if being updated
    if update_data.credential:
        try:
            credential = cred_collection.find_one({
                "_id": ObjectId(update_data.credential),
                "deleted_at": None
            })
            if not credential:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid credential. Credential not found."
                )
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid credential format."
            )

    update_dict = {k: v for k, v in update_data.dict(exclude_unset=True).items() if v is not None}
    update_dict["updated_at"] = datetime.utcnow()
    
    # Convert credential string to DBRef if provided
    if update_dict.get("credential"):
        update_dict["credential"] = DBRef("referring_provider_credentials", ObjectId(update_dict["credential"]))

    rp_collection.update_one({"_id": ObjectId(id), "specialty": "pcp"}, {"$set": update_dict})

    # Prepare response data
    updated_provider = {**existing, **update_dict}
    updated_provider["id"] = id
    
    # Convert DBRef credential back to string ID for response
    if updated_provider.get("credential") and hasattr(updated_provider["credential"], "id"):
        updated_provider["credential"] = str(updated_provider["credential"].id)

    response.status_code = status.HTTP_200_OK
    return api_response(
        PcpProviderSchema(**updated_provider),
        "Provider updated",
        "Success",
    )