Element Reference,Name,Section Display Name,Section Code,Coding Instructions,Target Value,Short Name,Data Type,Precision,Unit Of Measure,Selection Type,Default Value,Is Dynamic List,Missing Data,Is Harvested,Dataset,Data Source,Is Identifier,Is Base Element,Is Followup Element,Code,Code System,Code System Name,Vendor Instruction
2000,Last Name,Demographics,DEMOGRAPHICS,Indicate the patient's last name. Hyphenated names should be recorded with a hyphen.,The value on arrival at this facility,LastName,LN,50,,Single,,No,Report,Yes,,User,No,Yes,Yes,**********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
2010,First Name,Demographics,DEMOGRAPHICS,Indicate the patient's first name.,The value on arrival at this facility,FirstName,FN,50,,Single,,No,Report,Yes,,User,No,Yes,Yes,**********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
2020,Middle Name,Demographics,DEMOGRAPHICS,"Indicate the patient's middle name.

Note(s):
It is acceptable to specify the middle initial.

If there is no middle name given, leave field blank.

If there are multiple middle names, enter all of the middle names sequentially.

If the name exceeds 50 characters, enter the first 50 letters only.",The value on arrival at this facility,MidName,MN,50,,Single,,No,Report,Yes,,User,No,Yes,Yes,**********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
2050,Birth Date,Demographics,DEMOGRAPHICS,Indicate the patient's date of birth.,The value on arrival at this facility,DOB,DT,,,Single,,No,Report,Yes,,User,No,Yes,Yes,**********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
2030,SSN,Demographics,DEMOGRAPHICS,"Indicate the patient's United States Social Security Number (SSN).

Note(s):
If the patient does not have a US Social Security Number (SSN), leave blank and check 'SSN NA'.",The value on arrival at this facility,SSN,ST,9,,Single,,No,Report,Yes,,User,No,Yes,Yes,2.16.840.1.113883.4.1,2.16.840.1.113883.4.1,United States Social Security Number (SSN),
2031,SSN N/A,Demographics,DEMOGRAPHICS,Indicate if the patient does not have a United States Social Security Number (SSN).,The value on arrival at this facility,SSNNA,BL,,,Single,,No,Report,Yes,,User,No,Yes,Yes,2.16.840.1.113883.4.1,2.16.840.1.113883.4.1,United States Social Security Number (SSN),
2040,Patient ID,Demographics,DEMOGRAPHICS,"Indicate the number created and automatically inserted by the software that uniquely identifies this patient.

Note(s):
Once assigned to a patient at the participating facility, this number will never be changed or reassigned to a different patient. If the patient returns to the same participating facility or for follow up, they will receive this same unique patient identifier.",The value on arrival at this facility,NCDRPatientID,NUM,9,,Single,,No,Illegal,Yes,,User,Yes,Yes,Yes,2.16.840.1.113883.3.3478.4.842,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
2045,Other ID,Demographics,DEMOGRAPHICS,"Indicate an optional patient identifier, such as medical record number, that can be associated with the patient.",N/A,OtherID,ST,50,,Single,,No,Report,Yes,,User,No,Yes,Yes,2.16.840.1.113883.3.3478.4.843,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
2060,Sex,Demographics,DEMOGRAPHICS,Indicate the patient's sex at birth.,The value on arrival at this facility,Sex,CD,,,Single,,No,Report,Yes,,User,No,Yes,Yes,**********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
2065,Patient Zip Code,Demographics,DEMOGRAPHICS,"Indicate the patient's United States Postal Service zip code of their primary residence.

Note(s):
If the patient does not have a U.S. residence, or is homeless, leave blank and check 'Zip Code NA'.",The value on arrival at this facility,ZipCode,ST,5,,Single,,No,Report,Yes,,User,No,Yes,Yes,**********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
2066,Zip Code N/A,Demographics,DEMOGRAPHICS,"Indicate if the patient does not have a United States Postal Service zip code.

Note(s):
This includes patients who do not have a U.S. residence or are homeless.",The value on arrival at this facility,ZipCodeNA,BL,,,Single,,No,Report,Yes,,User,No,Yes,Yes,**********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
2070,Race - White,Demographics,DEMOGRAPHICS,"Indicate if the patient is White as determined by the patient/family.

Note(s):
If the patient has multiple race origins, specify them using the other race selections in addition to this one.",The value on arrival at this facility,RaceWhite,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,2106-3,2.16.840.1.113883.5.104,HL7 Race,
2071,Race - Black/African American,Demographics,DEMOGRAPHICS,"Indicate if the patient is Black or African American as determined by the patient/family.

Note(s):
If the patient has multiple race origins, specify them using the other race selections in addition to this one.",The value on arrival at this facility,RaceBlack,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,2054-5,2.16.840.1.113883.5.104,HL7 Race,
2073,Race - American Indian/Alaskan Native,Demographics,DEMOGRAPHICS,"Indicate if the patient is American Indian or Alaskan Native as determined by the patient/family.

Note(s):
If the patient has multiple race origins, specify them using the other race selections in addition to this one.",The value on arrival at this facility,RaceAmIndian,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1002-5,2.16.840.1.113883.5.104,HL7 Race,
2072,Race - Asian,Demographics,DEMOGRAPHICS,"Indicate if the patient is Asian as determined by the patient/family.

Note(s):
If the patient has multiple race origins, specify them using the other race selections in addition to this one.",The value on arrival at this facility,RaceAsian,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,2028-9,2.16.840.1.113883.5.104,HL7 Race,
2074,Race - Native Hawaiian/Pacific Islander,Demographics,DEMOGRAPHICS,"Indicate if the patient is Native Hawaiian or Pacific Islander as determined by the patient/family.

Note(s):
If the patient has multiple race origins, specify them using the other race selections in addition to this one.",The value on arrival at this facility,RaceNatHaw,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,2076-8,2.16.840.1.113883.5.104,HL7 Race,
2076,Hispanic or Latino Ethnicity,Demographics,DEMOGRAPHICS,"Indicate if the patient is of Hispanic or Latino ethnicity as determined by the patient/family. 

Note(s):
If the patient has multiple hispanic or latin ethnicity, specify them using the other ethnicity selections in addition to this one.",The value on arrival at this facility,HispOrig,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,2135-2,2.16.840.1.113883.5.50,HL7 Ethnicity,
14780,Original Patient ID,Demographics,DEMOGRAPHICS,"This is the ID generated when the patient was first submitted to the Registry. This field will be provided to vendors as part of the participant vendor migration process for all patients currently in the Registry. For patients submitted to the Registry the first time by a vendor, it should be populated with the NCDR Patient ID assigned by the vendor.",N/A,OrigPtID,NUM,9,,Single,,No,Illegal,Yes,,Automatic,No,Yes,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14781,Original NCDR Vendor,Demographics,DEMOGRAPHICS,"This is the vendor identifier of the vendor who first submitted the patient to the Registry. This field will be provided to vendors as part of the vendor migration process for all patients currently in the registry. For patients submitted to the Registry for the first time by a vendor, it should be populated with the Vendor Identifier of the submitting vendor.",N/A,OrigNCDRVen,ST,15,,Single,,No,Illegal,Yes,,Automatic,No,Yes,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
2999,Episode Unique Key,Episode Information,EOCINFO,Indicate the unique key associated with each patient episode record as assigned by the EMR/EHR or your software application.,N/A,EpisodeKey,ST,50,,Single,,No,Illegal,Yes,,Automatic,Yes,Yes,No,2.16.840.1.113883.3.3478.4.855,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
3000,Arrival Date,Episode Information,EOCINFO,Indicate the date the patient arrived at your facility.,N/A,ArrivalDate,DT,,,Single,,No,Illegal,Yes,,User,No,Yes,No,**********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"Patient must be at least 18 years old at the time of Arrival Date (3000)

Arrival Date (3000) must be Less than or Equal to Discharge Date (10100)"
3005,Health Insurance,Episode Information,EOCINFO,Indicate if the patient has health insurance.,The value on arrival at this facility,HealthIns,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,63513-6,2.16.840.1.113883.6.1,LOINC,
3010,Health Insurance Payment Source,Episode Information,EOCINFO,"Indicate the patient's health insurance payment type.

Note(s):
If the patient has multiple insurance payors, select all payors.

If there is uncertainty regarding how to identify a specific health insurance plan, please discuss with your billing department to understand how it should be identified in the registry.",The value on arrival at this facility,HIPS,CD,,,Multiple,,No,Report,Yes,,User,No,Yes,No,100001072,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
12846,Medicare Beneficiary Identifier,Episode Information,EOCINFO,"Indicate the patient's Medicare Beneficiary Identifier (MBI).

Note(s):
Enter the Medicare Beneficiary Identifier (MBI) for those patients insured by Medicare. Patients without Medicare will not have a MBI.",The value on arrival at this facility,MBI,ST,11,,Single,,No,Report,Yes,,User,No,Yes,No,2.16.840.1.113883.4.927,2.16.840.1.113883.4.927,Centers for Medicare & Medicaid Services,
3020,Patient Enrolled in Research Study,Episode Information,EOCINFO,Indicate if the patient is enrolled in an ongoing ACC - NCDR research study related to this registry. Intended for future use.,Any occurrence between arrival at this facility and discharge,EnrolledStudy,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100001095,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
3025,Research Study Name,Research Study,RSTUDY,"Indicate the research study name as provided by the research study protocol.

Note(s):
If the patient is in more than one research study, list each separately.
Intended for future use.",N/A,StudyName,ST,50,,Single,,No,Illegal,Yes,,User,No,Yes,No,100001096,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"Research Study Name (3025) must be a valid study name for LAAO.

A Research Study Name (3025) may only be entered/selected once"
3030,Research Study Patient ID,Research Study,RSTUDY,"Indicate the research study patient identification number as assigned by the research protocol.

Note(s):
If the patient is in more than one research study, list each separately.
Intended for future use.",N/A,StudyPtID,ST,50,,Single,,No,Illegal,Yes,,User,No,Yes,No,2.16.840.1.113883.3.3478.4.852,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14791,Admission for Left Atrial Appendage Occlusion Intervention,LAAO Intervention,LAAOINTERVENT,Indicate if the patient was admitted to the hospital specifically for an Left Atrial Appendage (LAA) Occlusion Intervention.,The value on arrival at this facility,LAAO_Adm,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,304566005,2.16.840.1.113883.6.96,SNOMED CT,
4005,CHA2DS2-VASc Congestive Heart Failure,CHA2DS2-VASc Risk Scores,CHA2DS2,"Indicate if the patient has been diagnosed with heart failure according to the CHA2DS2-VASc definition.

Note(s): A diagnosis of heart failure must be specifically documented in the medical record and not coded by the abstractor based upon patient symptoms. ",Any occurrence between 30 days prior to the procedure and the procedure,ChadCHF,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100001203,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4010,NYHA Functional Classification,CHA2DS2-VASc Risk Scores,CHA2DS2,"Indicate the patient's New York Heart Association (NYHA) Functional Classification based upon the physician documented classification at the time of the current procedure. 

Note(s):
The NYHA Functional Classification must be specifically documented in the medical record and not coded by the abstractor based upon patient symptoms.",The highest value on the first procedure in this admission,NYHA,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,420816009,2.16.840.1.113883.6.96,SNOMED CT,
4015,CHA2DS2-VASc LV Dysfunction,CHA2DS2-VASc Risk Scores,CHA2DS2,Indicate if the patient has been diagnosed with Left Ventricular (LV) Dysfunction according to the CHA2DS2-VASc definition.,Any occurrence between 30 days prior to the procedure and the procedure,ChadLVDysf,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100001204,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4020,CHA2DS2-VASc Hypertension,CHA2DS2-VASc Risk Scores,CHA2DS2,Indicate if the patient has been diagnosed with hypertension according to the CHA2DS2-VASc definition.,Any occurrence between 30 days prior to the procedure and the procedure,ChadHypertCont,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100001205,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4025,CHA2DS2-VASc Diabetes Mellitus,CHA2DS2-VASc Risk Scores,CHA2DS2,Indicate if the patient has been diagnosed with diabetes mellitus according to the CHA2DS2-VASc definition.,Any occurrence between 30 days prior to the procedure and the procedure,ChadDM,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100001206,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4030,CHA2DS2-VASc Stroke,CHA2DS2-VASc Risk Scores,CHA2DS2,"Indicate if the patient has been diagnosed with an ischemic stroke according to the CHA2DS2-VASc definition.

Note(s):
Patients with a history of stroke documented as undetermined in origin may be coded, but patients with a history of stroke documented as hemorrhagic in origin should not be coded.",Any occurrence between birth and the procedure,ChadStroke,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100001207,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4035,CHA2DS2-VASc TIA,CHA2DS2-VASc Risk Scores,CHA2DS2,Indicate if the patient has been diagnosed with a transient ischemic attack (TIA) according to the CHA2DS2-VASc definition.,Any occurrence between birth and the procedure,ChadTIA,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,*********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4040,CHA2DS2-VASc Thromboembolic Event,CHA2DS2-VASc Risk Scores,CHA2DS2,"Indicate if the patient has been diagnosed with a thromboembolic event (TE) according to the CHA2DS2-VASc definition.

Note(s):
A thromboembolic event is defined as a thrombus formed in a blood vessel that breaks loose and travels to occlude another vessel.",Any occurrence between birth and the procedure,ChadTE,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,*********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4045,CHA2DS2-VASc Vascular Disease,CHA2DS2-VASc Risk Scores,CHA2DS2,"Indicate if the patient has been diagnosed with vascular disease according to the CHA2DS2-VASc definition.

Note(s):
CHA2DS2-VASc Score Vascular disease (defined as prior MI, PAD, or aortic plaque) if Yes = + 1; If the clinician has utilized the presence of CAD, PCI, CABG, or carotid disease in the patient’s history as determining factors for selecting the CHA2DS2-VASc Vascular Disease element when considering the patient’s risk score, please note CAD, PCI, CABG, and carotid disease were not part of the original validated vascular disease criterion for the CHA2DS2-VASc score.",Any occurrence between birth and the procedure,ChadVascDis,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,*********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4050,Vascular Disease Type,CHA2DS2-VASc Risk Scores,CHA2DS2,"Indicate if the patient has a history of a prior myocardial infarction (MI), peripheral artery disease (PAD) or a known aortic plaque. If the patient has multiple vascular diseases, select all relevant disease types.

Note(s):
The following conditions are not part of the original definition of CHA2DS2-VASc Vascular Disease type: CAD, PCI, CABG, Carotid Disease, Carotid Stent, or Carotid Endarterectomy. Any finding of these conditions in the patient’s medical history does not automatically allow coding of yes.  Please code only if the physician utilized any of these conditions as part of the assessment when documenting or determining the patient’s CHA2DS2-VASc risk score.",Any occurrence between birth and the procedure,PriorVD,CD,,,Multiple,,No,Report,Yes,,User,No,Yes,No,27550009,2.16.840.1.113883.6.96,SNOMED CT,
4055,HAS-BLED Hypertension (Uncontrolled),HAS-BLED Risk Scores,HASBLED,Indicate if the patient has been diagnosed with uncontrolled hypertension as defined HAS- BLED Risk Model.,Any occurrence between 30 days prior to the procedure and the procedure,HBHyperUncont,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100001243,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4060,HAS-BLED Abnormal Renal Function,HAS-BLED Risk Scores,HASBLED,Indicate if the patient has been diagnosed with abnormal renal function as defined by the HAS-BLED Risk Model.,Any occurrence between 30 days prior to the procedure and the procedure,HBAbnRenal,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100001244,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4065,HAS-BLED Abnormal Liver Function,HAS-BLED Risk Scores,HASBLED,Indicate if the patient has been diagnosed with abnormal liver function as defined by the HAS-BLED Risk Model.,Any occurrence between 30 days prior to the procedure and the procedure,HBAbnLiver,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100001245,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4070,HAS-BLED Stroke,HAS-BLED Risk Scores,HASBLED,Indicate if the patient has experienced a stroke in the past as defined by the HAS-BLED Risk Model.,Any occurrence between birth and the procedure,HBStroke,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100001211,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14792,HAS-BLED Stroke Type,HAS-BLED Risk Scores,HASBLED,Indicate what type of stroke the patient has experienced in the past as defined by the HAS-BLED Risk Model.,Any occurrence between birth and the procedure,HBStrokeType,CD,,,Multiple,,No,Report,Yes,,User,No,Yes,No,100001211,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4095,HAS-BLED Bleeding,HAS-BLED Risk Scores,HASBLED,"Indicate if the patient has a history of a major bleeding event or predisposition to bleeding (eg, bleeding diathesis, anemia) as defined by the HAS-BLED Risk Model.

Note(s): Major bleeding defined as any bleeding requiring hospitalization, and/or causing a decrease in hemoglobin level > 2 g/dL, and/or requiring blood transfusion that was not hemorrhagic stroke.",Any occurrence between birth and the procedure,HBBleed,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100001212,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4100,HAS-BLED Labile INR,HAS-BLED Risk Scores,HASBLED,Indicate if the patient has experienced a labile international normalized ratios (INR) while on Warfarin therapy as defined by the HAS-BLED Risk Model.,Any occurrence between 30 days prior to the procedure and the procedure,HBLabINR,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100001024,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4105,HAS-BLED Alcohol,HAS-BLED Risk Scores,HASBLED,Indicate if the patient uses alcohol in excess as defined by the HAS-BLED Risk Score.,Any occurrence between 30 days prior to the procedure and the procedure,HBAlcohol,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100001213,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4110,HAS-BLED Drugs - Antiplatelet,HAS-BLED Risk Scores,HASBLED,"Indicate if the patient is taking antiplatelet medications.

Note(s): If the patient is taking any dosage of aspirin code as ""Yes"".",Any occurrence between 30 days prior to the procedure and the procedure,HBDrugAP,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100001242,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4115,HAS-BLED Drugs - NSAIDS,HAS-BLED Risk Scores,HASBLED,Indicate if the patient is taking a non-steroidal anti-inflammatory drug (NSAID).,Any occurrence between 30 days prior to the procedure and the procedure,HBDrugNSAID,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100001241,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14793,Increased Fall Risk,Additional Stroke and Bleeding Risk Factors,STROKEBLEED,Indicate if the patient has an increased susceptibility to falling that may cause physical harm as defined by the American Geriatrics Society.,Any occurrence between 12 months prior to the procedure and start of the procedure,IncrFallRisk,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14794,Clinically Relevant Bleeding Event,Additional Stroke and Bleeding Risk Factors,STROKEBLEED,Indicate if the patient had any of the following associated with a Clinically Relevant Bleeding Event.,Any occurrence between birth and the procedure,ClinicBleedEvent,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14796,Bleeding Event Type,Additional Stroke and Bleeding Risk Factors,STROKEBLEED,,Any occurrence between birth and the procedure,BleedEventType,CD,,,Multiple,,No,Report,Yes,,User,No,Yes,No,131148009,2.16.840.1.113883.6.96,SNOMED CT,
14797,Genetic Coagulopathy,Additional Stroke and Bleeding Risk Factors,STROKEBLEED,Indicate if the patient has been diagnosed with a genetic coagulopathy.,Any occurrence between birth and the procedure,GeneticCoag,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14798,Concurrent Anticoagulant Therapy,Additional Stroke and Bleeding Risk Factors,STROKEBLEED,Indicate if the patient was using any anticoagulant medication at the time of the clinically relevant bleeding event.,Any occurrence between birth and the procedure,ConAntiCoagTx,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,182764009,2.16.840.1.113883.6.96,SNOMED CT,
13709,Atrial Fibrillation,Rhythm History,RHYTHM,Indicate if the patient has a history of atrial fibrillation.,Any occurrence between birth and current procedure,AFibInd,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,49436004,2.16.840.1.113883.6.96,SNOMED CT,
4400,Atrial Fibrillation Classification,Rhythm History,RHYTHM,Indicate the type of atrial fibrillation experienced by the patient.,Any occurrence between birth and the first procedure in this admission,AFibClass,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,100000935,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4380,Valvular Atrial Fibrillation,Rhythm History,RHYTHM,"Indicate if the patient has atrial fibrillation occurring in the setting of valvular heart disease and believed to be, at least in part, directly attributable to valvular heart disease (especially mitral valvular disease).",Any occurrence between birth and the procedure,ValvularAF,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100001118,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14799,History of Rheumatic Valve Disease,Rhythm History,RHYTHM,Indicate if the patient has a history of rheumatic valve disease.,Any occurrence between birth and the procedure,HxRHVD,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,16063004,2.16.840.1.113883.6.96,SNOMED CT,
4385,History of Mitral Valve Replacement,Rhythm History,RHYTHM,Indicate if the patient has a history of mitral valve replacement either via open surgical or a percutaneous transcatheter intervention.  ,Any occurrence between birth and the procedure,HxMVReplace,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,53059001,2.16.840.1.113883.6.96,SNOMED CT,
4390,Mechanical Valve in Mitral Position,Rhythm History,RHYTHM,Indicate if the patient has a mechanical valve placed in the mitral position. ,Any occurrence between birth and the procedure,MechValveMitPos,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,431339008,2.16.840.1.113883.6.96,SNOMED CT,
4395,History of Mitral Valve Repair,Rhythm History,RHYTHM,"Indicate if the patient has a history of mitral valve repair, specifically via the surgical route. Either a surgical repair of a mitral valve leaflet or mitral annuloplasty qualifies as repair.",Any occurrence between birth and the procedure,HxMVRepair,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,384641003,2.16.840.1.113883.6.96,SNOMED CT,
4410,Attempt at Atrial Fibrillation Termination,Rhythm History,RHYTHM,Indicate if the patient has had previous attempts to terminate the atrial fibrillation. ,Any occurrence between birth and the procedure,PrevAFibTerm,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100000936,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4415,Atrial Fibrillation Termination - Pharmacologic Cardioversion,Rhythm History,RHYTHM,"Indicate if the patient has a history of pharmacological cardioversion. 

",Any occurrence between birth and the procedure,PrevAFibTermPC,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,440142000:363702006=49436004,2.16.840.1.113883.6.96,SNOMED CT,
4420,Atrial Fibrillation Termination - DC Cardioversion,Rhythm History,RHYTHM,Indicate if the patient has a history of direct current (DC) cardioversion.,Any occurrence between birth and the procedure,PrevAFibTermDC,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,180325003:363702006=49436004,2.16.840.1.113883.6.96,SNOMED CT,
4425,Atrial Fibrillation Termination - Catheter Ablation,Rhythm History,RHYTHM,Indicate if the patient has a history of catheter ablation for termination of atrial fibrillation.,Any occurrence between birth and the procedure,PrevAFibTermCA,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,18286008:363702006=49436004,2.16.840.1.113883.6.96,SNOMED CT,
4430,Atrial Fibrillation Most Recent Catheter Ablation Date,Rhythm History,RHYTHM,"Indicate the date of the most recent attempt to terminate the atrial fibrillation via catheter ablation. 

Note(s):
If the month or day is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had ""most recent ablation"" documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).",Any occurrence between birth and the procedure,AFibCathAblDate,DT,,,Single,,No,Report,Yes,,User,No,Yes,No,18286008:363702006=49436004,2.16.840.1.113883.6.96,SNOMED CT,Atrial Fibrillation Most Recent Catheter Ablation Date
4435,Prior Catheter Ablation Strategy,Rhythm History,RHYTHM,"Indicate the previously attempted catheter ablation strategy/strategies used to treat the atrial fibrillation.

Note(s):
The strategies that should be collected in your application are controlled by Ablation Strategy Master file. This file is maintained by the NCDR and will be made available on the internet for downloading and importing/updating into your application.",Any occurrence between birth and the procedure,AFibPriorAblStrategyCode,CD,,,Multiple,,Yes,Report,Yes,,User,No,Yes,No,18286008:363702006=49436004,2.16.840.1.113883.6.96,SNOMED CT,
4440,Atrial Fibrillation Termination - Surgical Ablation,Rhythm History,RHYTHM,Indicate if the patient has a history of surgical ablation. ,Any occurrence between birth and the procedure,PrevAFibTermSA,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,233163003:363702006=49436004,2.16.840.1.113883.6.96,SNOMED CT,
4445,"Atrial Fibrillation, Most Recent Surgical Ablation Date",Rhythm History,RHYTHM,"Indicate the date of the most recent attempt to terminate the atrial fibrillation via surgical ablation. 

Note(s):
If the month or day is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had ""most recent surgical ablation"" documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).",Any occurrence between birth and the procedure,AFibSurgAblDate,DT,,,Single,,No,Report,Yes,,User,No,Yes,No,233163003:363702006=49436004,2.16.840.1.113883.6.96,SNOMED CT,Atrial Fibrillation Most Recent Surgical Ablation Date (4445) must be Less than or Equal to the Procedure Start Date and Time (7000)
4450,Atrial Flutter,Rhythm History,RHYTHM,Indicate if the patient has a history of atrial flutter.,Any occurrence between birth and the procedure,AFlutter,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,5370000,2.16.840.1.113883.6.96,SNOMED CT,
4455,Atrial Flutter Classification,Rhythm History,RHYTHM,Indicate the predominate type of atrial flutter experienced by the patient. ,Any occurrence between birth and the procedure,AFlutterType,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,100000938,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4460,Attempt at Atrial Flutter Termination,Rhythm History,RHYTHM,Indicate if the patient has had previous attempts to terminate the atrial flutter.,Any occurrence between birth and the procedure,PrevAFLTerm,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100000937,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4465,Atrial Flutter Termination - Pharmacologic Cardioversion,Rhythm History,RHYTHM,Indicate if the patient has a history of pharmacologic cardioversion to terminate the atrial flutter.,Any occurrence between birth and the procedure,PrevAFLTermPC,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,440142000:363702006=5370000,2.16.840.1.113883.6.96,SNOMED CT,
4470,Atrial Flutter Termination - DC Cardioversion,Rhythm History,RHYTHM,Indicate if the patient has a history of DC cardioversion to terminate the atrial flutter.,Any occurrence between birth and the procedure,PrevAFLTermDC,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,180325003:363702006=5370000,2.16.840.1.113883.6.96,SNOMED CT,
4475,Atrial Flutter Termination - Catheter Ablation,Rhythm History,RHYTHM,Indicate if the patient has a history of catheter ablation to terminate the atrial flutter.,Any occurrence between birth and the procedure,PrevAFLTermCA,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,18286008:363702006=5370000,2.16.840.1.113883.6.96,SNOMED CT,
4480,Atrial Flutter Most Recent Catheter Ablation Date,Rhythm History,RHYTHM,"Indicate the date of the most recent catheter ablation.

Note(s):
If the month or day is unknown, please code 01/01/YYYY. If the specific year is unknown in the current record, the year may be estimated based on timeframes found in prior medical record documentation (Example: If the patient had ""most recent ablation"" documented in a record from 2011, then the year 2011 can be utilized and coded as 01/01/2011).",Any occurrence between birth and the procedure,AFibFlutterCathAblDate,DT,,,Single,,No,Report,Yes,,User,No,Yes,No,18286008:363702006=5370000,2.16.840.1.113883.6.96,SNOMED CT,Atrial Flutter Most Recent Catheter Ablation Date (4480) must be Less than or Equal to the Procedure Start Date and Time (7000)
14802,Cardiac Structural Intervention,Interventions,INTERVENT,Indicate if the patient has a history of cardiac structural interventions (percutaneously or surgically).,Any occurrence between birth and the procedure,CardStrucInterv,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14803,Cardiac Structural Intervention Type,Interventions,INTERVENT,Indicate the type of prior cardiac structural intervention. ,Any occurrence between birth and the procedure,CardStrucIntervType,CD,,,Multiple,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14804,Left Atrial Appendage Occlusion Intervention,Interventions,INTERVENT,"Indicate if the patient has a history of a left atrial appendage occlusion intervention.

Note(s):   Previously “Aborted” LAA interventions should be captured in this element.",Any occurrence between birth and the procedure,LAAOInterv,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14806,Left Atrial Appendage Intervention Type,Interventions,INTERVENT,Indicate the type of prior left atrial appendage occlusion intervention.,Any occurrence between birth and the procedure,LAAOType,CD,,,Multiple,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4565,Cardiomyopathy (CM),Additional History and Risk Factors,AHISTORYANDRISK,Indicate if the patient has a history of cardiomyopathy.,Any occurrence between birth and the procedure,CM,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,85898001,2.16.840.1.113883.6.96,SNOMED CT,
4570,Cardiomyopathy Type,Additional History and Risk Factors,AHISTORYANDRISK,"Indicate the type of cardiomyopathy experienced by the patient.

Note(s):
If the patient has had multiple cardiomyopathies, select all applicable types.",Any occurrence between birth and the procedure,PriorCMType,CD,,,Multiple,,No,Report,Yes,,User,No,Yes,No,100000953,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
4575,Chronic Lung Disease,Additional History and Risk Factors,AHISTORYANDRISK,"Indicate if the patient has a history of chronic lung disease.

Note(s):
A history of chronic inhalation reactive disease (asbestosis, mesothelioma, black lung disease or pneumoconiosis) may qualify as chronic lung disease. Radiation induced pneumonitis or radiation fibrosis also qualifies as chronic lung disease. A history of atelectasis is a transient condition and does not qualify.",Any occurrence between birth and the procedure,ChronicLungDisease,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,413839001,2.16.840.1.113883.6.96,SNOMED CT,
4285,Coronary Artery Disease,Additional History and Risk Factors,AHISTORYANDRISK,Indicate if the patient has a history of coronary artery disease (CAD).,Any occurrence between birth and the procedure,CAD,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,53741008,2.16.840.1.113883.6.96,SNOMED CT,
4580,Sleep Apnea,Additional History and Risk Factors,AHISTORYANDRISK,"Indicate if the patient has a history of sleep apnea that has been diagnosed by a sleep study.

Note(s):
Code ""No"" if sleep apnea has been surgically corrected.

CPAP or BiPAP therapy is not a requirement to code ""Yes"" for sleep apnea.

Both Obstructive Sleep Apnea (recurrent collapse of the pharynx during sleep) and Central Sleep apnea: (transient cessation of neural drive to respiratory muscles) should be considered.  Capture patients with prescribed home therapy despite frequency of use. Do not capture suspected sleep apnea or that reported by family members as sleep apnea. Sleep apnea must be diagnosed by a physician.",Any occurrence between birth and the procedure,SleepApnea,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,73430006,2.16.840.1.113883.6.96,SNOMED CT,
4585,Sleep Apnea Recommended Treatment Followed,Additional History and Risk Factors,AHISTORYANDRISK,"Indicate if the patient followed the sleep apnea treatment plan recommended.

Note(s):
CPAP or BiPAP therapy is not  a requirement to code 'Yes' for sleep apnea. 
Both Obstructive Sleep Apnea (recurrent collapse of the pharynx during sleep) and Central Sleep Apnea (transient cessation of neural drive to respiratory muscles) should be considered. 
Capture patients with prescribed home therapy despite frequency of use.
Code 'No' if sleep apnea has been surgically corrected.",Any occurrence between birth and the procedure,SleepApneaRxFollowed,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100001098,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14824,Epicardial Approach Considered,Epicardial Access Assessment,EPICARDIAL,Indicate if an epicardial approach to the left atrial appendage intervention was considered for this episode of care.,Any occurrence between birth and the procedure,EpicardialAppCons,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14823,Medical Conditions,Epicardial Access Assessment,EPICARDIAL,Indicate if any of the following medical conditions were present.,Any occurrence between birth and the procedure,MedCond,CD,,,Multiple,,No,Report,Yes,,User,No,Yes,No,362965005,2.16.840.1.113883.6.96,SNOMED CT,
14825,Lupus Erythematosus,Epicardial Access Assessment,EPICARDIAL,Indicate if an Lupus Erythematosus was the condition for which an epicardial approach to the left atrial appendage intervention was considered for this episode of care.,Any occurrence between birth and the procedure,LupusCons,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,200936003,2.16.840.1.113883.6.96,SNOMED CT,
5100,Atrial Rhythm,Diagnostic Studies,DIAGNOSTICS,Indicate the patient's atrial rhythm at the start of the procedure.,The last value within 30 days prior to the first procedure in this admission,AtrialRhythm,CD,,,Multiple,,No,Report,Yes,,User,No,Yes,No,106068003,2.16.840.1.113883.6.96,SNOMED CT,
5110,LVEF Assessed,Diagnostic Studies,DIAGNOSTICS,Indicate if a left ejection fraction percentage has been assessed.,The last value between 90 days prior to the start of the current procedure and the start of procedure,LVEFAssessed,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100001027,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
5115,Most Recent LVEF %,Diagnostic Studies,DIAGNOSTICS,Indicate the most recent left ventricular ejection fraction.,The last value between 90 days prior to the start of the current procedure and the start of procedure,LVEF,PQ,"2,0",%,Single,,No,Report,Yes,,User,No,Yes,No,10230-1,2.16.840.1.113883.6.1,LOINC,
5120,Transthoracic Echo (TTE) Performed,Diagnostic Studies,DIAGNOSTICS,Indicate if a transthoracic echocardiogram (TTE) was performed prior to the procedure.,The last value between 90 days prior to the start of the current procedure and the start of procedure,TTEPerf,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,433236007,2.16.840.1.113883.6.96,SNOMED CT,
5125,Most Recent TTE Date,Diagnostic Studies,DIAGNOSTICS,Indicate the date of the most recent transthoracic echocardiogram (TTE) performed and used to evaluate the patient for this intervention.,The last value between 90 days prior to the start of the current procedure and the start of procedure,TTEDate,DT,,,Single,,No,Report,Yes,,User,No,Yes,No,433236007,2.16.840.1.113883.6.96,SNOMED CT,Most Recent TTE Date (5125) must be Less than or Equal to the Procedure Start Date and Time (7000)
5170,Baseline Imaging Performed,Diagnostic Studies,DIAGNOSTICS,Indicate if pre-procedure imaging was performed.,The last value between 90 days prior to the start of the current procedure and the start of procedure,BaselineImagingPerf,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,363679005,2.16.840.1.113883.6.96,SNOMED CT,
5175,Baseline CT Performed,Diagnostic Studies,DIAGNOSTICS,Indicate if pre-procedure imaging was performed via CT.,The last value between 90 days prior to the start of the current procedure and the start of procedure,CTPerformed,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,58744-4,2.16.840.1.113883.6.1,LOINC,
5180,Most Recent CT Date,Diagnostic Studies,DIAGNOSTICS,Indicate the date of the most recent CT imaging.,The last value between 90 days prior to the start of the current procedure and the start of procedure,CTImagingDate,DT,,,Single,,No,Report,Yes,,User,No,Yes,No,58744-4,2.16.840.1.113883.6.1,LOINC,Most Recent CT Date (5180) must be Less than or Equal to the Procedure Start Date and Time (7000)
5185,Baseline MRI Performed,Diagnostic Studies,DIAGNOSTICS,Indicate if pre-procedure imaging was performed via MRI.,The last value between 90 days prior to the start of the current procedure and the start of procedure,MRPerformed,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,36482-8,2.16.840.1.113883.6.1,LOINC,
5190,Most Recent MRI Date,Diagnostic Studies,DIAGNOSTICS,Indicate the date of the most recent MRI imaging.,The last value between 90 days prior to the start of the current procedure and the start of procedure,MRDate,DT,,,Single,,No,Report,Yes,,User,No,Yes,No,36482-8,2.16.840.1.113883.6.1,LOINC,Most Recent MRI Date (5190) must be Less than or Equal to the Procedure Start Date and Time (7000)
14826,Intracardiac Echo Performed,Diagnostic Studies,DIAGNOSTICS,Indicate if pre-procedure imaging was performed via intracardiac echo (ICE).,The last value between 90 days prior to the start of the current procedure and the start of procedure,ICEPerf,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,448761005,2.16.840.1.113883.6.96,SNOMED CT,
14827,Date of Intracardiac Echo,Diagnostic Studies,DIAGNOSTICS,Indicate the date of the most recent intracardiac echo (ICE).,The last value between 90 days prior to the start of the current procedure and the start of procedure,ICEDate,DT,,,Single,,No,Report,Yes,,User,No,Yes,No,448761005,2.16.840.1.113883.6.96,SNOMED CT,
6000,Height,Physical Exam and Labs,PREPROCLABS,Indicate the patient's height in centimeters.,The last value prior to the start of the first procedure,Height,PQ,"5,2",cm,Single,,No,Report,Yes,,User,No,Yes,No,8302-2,2.16.840.1.113883.6.1,LOINC,
6005,Weight,Physical Exam and Labs,PREPROCLABS,Indicate the patient's weight in kilograms. ,The last value prior to the start of the first procedure,Weight,PQ,"5,2",kg,Single,,No,Report,Yes,,User,No,Yes,No,3141-9,2.16.840.1.113883.6.1,LOINC,
6010,Pulse,Physical Exam and Labs,PREPROCLABS,Indicate the patient's heart rate (beats per minute).,The last value prior to the start of the first procedure,Pulse,PQ,"3,0",bpm,Single,,No,Report,Yes,,User,No,Yes,No,8867-4,2.16.840.1.113883.6.1,LOINC,
6015,Systolic BP,Physical Exam and Labs,PREPROCLABS,Indicate the patient's systolic blood pressure in mmHg. ,The last value prior to the start of the first procedure,SystolicBP,PQ,"3,0",mm[Hg],Single,,No,Report,Yes,,User,No,Yes,No,8480-6,2.16.840.1.113883.6.1,LOINC,
6020,Diastolic BP,Physical Exam and Labs,PREPROCLABS,Indicate the patient's  diastolic blood pressure in mmHg. ,The last value prior to the start of the first procedure,DiastolicBP,PQ,"3,0",mm[Hg],Single,,No,Report,Yes,,User,No,Yes,No,8462-4,2.16.840.1.113883.6.1,LOINC,
6030,Hemoglobin,Physical Exam and Labs,PREPROCLABS,"Indicate the hemoglobin (Hgb) value in g/dL.

Note(s):  
This may include POC (Point of Care) testing results or results obtained prior to arrival at this facility.",The last value within 30 days prior to the first procedure in this admission,HGB,PQ,"4,2",g/dL,Single,,No,Report,Yes,,User,No,Yes,No,718-7,2.16.840.1.113883.6.1,LOINC,
6031,Hemoglobin Not Drawn,Physical Exam and Labs,PREPROCLABS,Indicate if the hemoglobin was not drawn.,The last value within 30 days prior to the first procedure in this admission,HGBND,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,718-7,2.16.840.1.113883.6.1,LOINC,
6040,Prothrombin Time (PT),Physical Exam and Labs,PREPROCLABS,"Indicate the last prothrombin time (PT) in seconds.

Note(s):
This may include POC (Point of Care) testing results.

Most recent values prior to the start of the procedure.",The last value between 1 day prior to the procedure and the current procedure,PT,PQ,"3,1",sec,Single,,No,Report,Yes,,User,No,Yes,No,5902-2,2.16.840.1.113883.6.1,LOINC,
6041,Prothrombin Not Drawn,Physical Exam and Labs,PREPROCLABS,Indicate if prothrombin (PT) was not drawn.,N/A,PTND,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,5902-2,2.16.840.1.113883.6.1,LOINC,
6045,International Normalized Ratio (INR),Physical Exam and Labs,PREPROCLABS,"Indicate the international normalized ratio (INR) if the patient was on routine Warfarin/Coumadin therapy.

Note(s):
This may include POC (Point of Care) testing results.

Most recent values prior to the start of the procedure.",The last value between 1 day prior to the procedure and the current procedure,INR,PQ,"3,1",,Single,,No,Report,Yes,,User,No,Yes,No,34714-6,2.16.840.1.113883.6.1,LOINC,
6046,International Normalized Ratio Not Drawn,Physical Exam and Labs,PREPROCLABS,Indicate if INR was not drawn.,N/A,INRND,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,34714-6,2.16.840.1.113883.6.1,LOINC,
6050,Creatinine,Physical Exam and Labs,PREPROCLABS,"Indicate the creatinine (Cr) level mg/dL.

Note(s):
This may include POC (Point of Care) testing results or results obtained prior to arrival at this facility.",The last value between 30 days prior to the procedure and the current procedure,PreProcCreat,PQ,"4,2",mg/dL,Single,,No,Report,Yes,,User,No,Yes,No,2160-0,2.16.840.1.113883.6.1,LOINC,
6051,Creatinine Not Drawn,Physical Exam and Labs,PREPROCLABS,Indicate if a creatinine level was not drawn.,N/A,PreProcCreatND,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,2160-0,2.16.840.1.113883.6.1,LOINC,
14210,Albumin,Physical Exam and Labs,PREPROCLABS,Indicate the total albumin (in g/dL).,The last value between 30 days prior to the procedure and the current procedure,Albumin,PQ,"3,1",g/dL,Single,,No,Report,Yes,,User,No,Yes,No,52454007,2.16.840.1.113883.6.96,SNOMED CT,
14211,Albumin Not Drawn,Physical Exam and Labs,PREPROCLABS,Indicate true if the total albumin was not drawn,N/A,Albumin_ND,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,52454007,2.16.840.1.113883.6.96,SNOMED CT,
13213,Platelet Count,Physical Exam and Labs,PREPROCLABS,"Indicate the pre-procedure platelet count in platelets per microliter.
",The last value between 30 days prior to the procedure and the current procedure,PlateletCt,PQ,"6,0",μL,Single,,No,Report,Yes,,User,No,Yes,No,777-3,2.16.840.1.113883.6.1,LOINC,
13214,Platelet Count Not Drawn,Physical Exam and Labs,PREPROCLABS,"Indicate if a platelet count was not drawn prior to the procedure.
",N/A,PlateletCtND,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,777-3,2.16.840.1.113883.6.1,LOINC,
14805,Modified Rankin Scale,Physical Exam and Labs,PREPROCLABS,Indicate the patient’s functional ability according to the modified Rankin Scale (mRS) administered pre-procedure.,The last value between 30 days prior to the procedure and the current procedure,RankinScale,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,75859-9,2.16.840.1.113883.6.1,LOINC,
9130,Modified Rankin Scale Not Administered,Physical Exam and Labs,PREPROCLABS,Indicate if the Modified Rankin Scale was not administered after the current procedure.,N/A,PostProc_RankinScaleNA,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,75859-9,2.16.840.1.113883.6.1,LOINC,
6985,Pre-procedure Medication Code,Pre-Procedure Medications,PREPROCMED,Indicate the NCDR-assigned IDs for the medications prescribed within 24 hours prior the procedure.,Any occurrence within 24 hours prior to start of current procedure ,MedID,CD,,,Single,,Yes,Report,Yes,,User,No,Yes,No,100013057,2.16.840.1.113883.3.3478.6.1,ACC NCDR,Pre-procedure Medication Code (6985) should not be duplicated in an episode
14883,Medication Administered,Pre-Procedure Medications,PREPROCMED,"Indicate the prescribing history and administration status (past, current, held, never) of each medication.",Any occurrence within 24 hours prior to start of current procedure ,PreMedAdmin,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,432102000,2.16.840.1.113883.6.96,SNOMED CT,"When a Pre-procedure Medication Code (6985) is selected, Medication Administered (14883) cannot be Null"
14828,Transesophageal Echocardiogram (TEE) Performed,Pre-Procedure Diagnostics,PROCDIAGNOSTICS,"Indicate if transesophageal echocardiogram (TEE) was performed prior to the device insertion or attempted device insertion during the current procedure.

Note: Prior to current procedure refers to prior to start of current procedure.",The last value between 1 week prior to current procedure and current procedure,TEEPerfLAAO,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,105376000,2.16.840.1.113883.6.96,SNOMED CT,
14829,Most Recent TEE Date,Pre-Procedure Diagnostics,PROCDIAGNOSTICS,Indicate the date of the most recent transesophageal echocardiogram (TEE) performed prior to the device insertion or attempted device insertion during the current procedure.,The last value between 1 week prior to current procedure and current procedure,TEEDateLAAO,DT,,,Single,,No,Report,Yes,,User,No,Yes,No,105376000,2.16.840.1.113883.6.96,SNOMED CT,Most Recent TEE Date (14829) must be Less than or Equal to the Procedure Start Date and Time (7000)
14838,Atrial Thrombus Detected,Pre-Procedure Diagnostics,PROCDIAGNOSTICS,Indicate if an atrial thrombus was detected or suspected.,The last value between 1 week prior to current procedure and current procedure,ProcAtrialThromDetect,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,396339007:123005000=59652004,2.16.840.1.113883.6.96,SNOMED CT,
14830,Left Atrial Appendage Occlusion Orifice width,Pre-Procedure Diagnostics,PROCDIAGNOSTICS,Indicate the maximal orifice width of the left atrial appendage (LAA) in mm.,The last value between 1 week prior to current procedure and current procedure,LAAO_OrWid,PQ,"3,1",mm,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
7000,Procedure Start Date and Time,Procedure,PROC,Indicate the procedure start time as the time that the patient entered the location in which the procedure is intended to be performed.,Any occurrence on current procedure,ProcedureStartDateTime,TS,,,Single,,No,Illegal,Yes,,User,No,Yes,No,**********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"Procedure Start Date and Time (7000) must be Greater than or Equal to Arrival Date (3000)

Procedure Start Date and Time (7000) must be Less than or Equal to Procedure End Date and Time (7005)

Procedure Start Date and Time (7000) must be Less than or Equal to Discharge Date (10100)"
7005,Procedure End Date and Time,Procedure,PROC,"Indicate the ending date and time at which the operator completes the procedure and breaks scrub at the end of the procedure.

Note(s):
If more than one operator is involved in the case then use the date and time the last operator breaks scrub for the last time.",The value on current procedure,ProcedureEndDateTime,TS,,,Single,,No,Report,Yes,,User,No,Yes,No,**********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"Procedure End Date and Time (7005) must be Less than or Equal to Discharge Date (10100)

Procedure End Date and Time (7005) and Procedure Start Date and Time (7000) must not overlap on multiple procedures"
14732,Shared Decision Making,Procedure,PROC,Indicate if shared decision making was performed for the procedure. ,The value on current procedure,SDM_Proc,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14733,Shared Decision Making Tool Used,Procedure,PROC,Indicate if a shared decision making tool was used.,The value on current procedure,SDM_Tool,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,415806002,2.16.840.1.113883.6.96,SNOMED CT,
14734,Shared Decision Making Tool Name,Procedure,PROC,"Indicate what tool was used. 
If the tool used is not in the drop-down list, <NAME_EMAIL> to have a selection added.",The value on current procedure,SDM_Tool_Name,CD,,,Single,,Yes,Report,Yes,,User,No,Yes,No,405083000,2.16.840.1.113883.6.96,SNOMED CT,
12871,Procedure Location,Procedure,PROC,"Indicate the location where the procedure was performed.
",The value on current procedure,ProcedureLocation,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
7130,Sedation,Procedure,PROC,Indicate the type of sedation used for the intervention.,The value on current procedure,Anesthesia,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,72641008,2.16.840.1.113883.6.96,SNOMED CT,
14837,LAA Occlusion Indication,Procedure,PROC,"Provide the documented indication for the left atrial appendage (LAA) occlusion procedure.

",The value on current procedure,ProcLAAOInd,CD,,,Multiple,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14834,Procedure Canceled,Procedure,PROC,"Indicate if the procedure was canceled after the patient had entered the procedure room AND before venous or epicardial access was obtained.

",The value on current procedure,ProcCanceled,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14833,Procedure Canceled Reason,Procedure,PROC,Indicate the reason(s) why the procedure was canceled.,The value on current procedure,ProcCanceledReason,CD,,,Multiple,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14831,Procedure Aborted,Procedure,PROC,"Indicate if the LAAO intervention was aborted at any time after venous or epicardial access was obtained.

",The value on current procedure,ProcAborted,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"If Device Successfully Deployed (14968) is 'Yes' for at least one device under the procedure, then Procedure Aborted (14831) must be 'No'.

If Device Successfully Deployed (14968) is 'No' for all the devices under the procedure, then Procedure Aborted (14831) must be 'Yes'.

If Procedure Aborted (14831) is 'No', at least one device within the procedure must have Device Successfully Deployed (14968) equal to 'Yes'"
14832,Procedure Aborted Reason,Procedure,PROC,Indicate the reason(s) why the procedure was aborted.,The value on current procedure,ProcAbortedReason,CD,,,Multiple,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14848,Device Margin Residual Leak,Procedure,PROC,Indicate the size (in mm) of the residual leak noted at the device margin.,The value on current procedure,ResidualLeak,PQ,"3,1",mm,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14849,Device Margin Residual Leak Not Assessed,Procedure,PROC,Indicate if the device margin was not assessed for any potential residual leak.,The value on current procedure,ResidualLeakNA,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
7200,Guidance Method,Procedure,PROC,"Indicate the assigned identification number associated with the guidance method used for this procedure.

Note(s):
The method(s) that should be collected in your application are controlled by a Guidance Method Master file. This file is maintained by the NCDR and will be made available on the internet for downloading and importing/updating into your application.",The value on current procedure,GuidanceMethodID,CD,,,Multiple,,Yes,Report,Yes,,User,No,Yes,No,100000919,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14846,Conversion to Open Heart Surgery,Procedure,PROC,"Indicate if this procedure converted to open heart surgery.
",The value on current procedure,OHSConversion,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14847,Conversion to Open Heart Surgery Reason,Procedure,PROC,"Indicate the reason why the procedure converted to open heart surgical access.
",The value on current procedure,OHSConversionReason,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14855,Concomitant Procedures Performed,Procedure,PROC,"Indicate if other procedures (Afib Ablation, ICD, PCI, TAVR or TMVR) were performed during the same lab visit as this procedure.
",The value on current procedure,ConcomitantProcPerf,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100001271,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14857,Concomitant Procedures,Procedure,PROC,Indicate which specific other procedures were performed during the same lab visit.,The value on current procedure,ConcomitantProcType,CD,,,Multiple,,Yes,Report,Yes,,User,No,Yes,No,100013075,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14861,Operator Last Name,Operator Information,OPRINFO,"Indicate the last name of operator.

Note(s):
If the name exceeds 50 characters, enter the first 50 characters only.",The value on current procedure,OperA_LastName2,LN,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,Operator Last Name (14861) cannot be Null
14860,Operator First Name,Operator Information,OPRINFO,"Indicate the first name of operator.

Note(s):
If the name exceeds 50 characters, enter the first 50 characters only.",The value on current procedure,OperA_FirstName2,FN,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,Operator First Name (14860) cannot be Null
14862,Operator Middle Name,Operator Information,OPRINFO,"Indicate the middle name of operator.

Note(s):
If the name exceeds 50 characters, enter the first 50 characters only.",The value on current procedure,OperA_MidName2,MN,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14863,LAAO Operator NPI,Operator Information,OPRINFO,"Indicate the National Provider Identifier (NPI) of the operator who is performing the procedure. NPI's, assigned by the Centers for Medicare and Medicaid Services (CMS), are used to uniquely identify physicians for Medicare billing purposes.",The value on current procedure,OperA_NPI2,NUM,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"LAAO Operator NPI (14863) cannot be Null

LAAO Operator NPI (14863) may only be entered/selected once."
15433,Fellow Last Name,Fellow Information,FELLOW,"Indicate the last name of the Fellow-in-Training operator.

Note(s):
If the name exceeds 50 characters, enter the first 50 characters only.",The value on current procedure,FIT_LastName,LN,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
15434,Fellow First Name,Fellow Information,FELLOW,"Indicate the first name of the Fellow-in-Training operator.

Note(s):
If the name exceeds 50 characters, enter the first 50 characters only.",The value on current procedure,FIT_FirstName,FN,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
15435,Fellow Middle Name,Fellow Information,FELLOW,"Indicate the middle name of the Fellow-in-Training operator.

Note(s):
If the name exceeds 50 characters, enter the first 50 characters only.",The value on current procedure,FIT_MidName,MN,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
15436,Fellow NPI,Fellow Information,FELLOW,"Indicate the National Provider Identifier (NPI) of the Fellow-in-Training operator who is performing the procedure. NPI's, assigned by the Centers for Medicare and Medicaid Services (CMS), are used to uniquely identify physicians for Medicare billing purposes.",The value on current procedure,FIT_NPI,NUM,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"Fellow NPI (15436) may only be entered/selected once.

The LAAO Operator NPI (14863) cannot be equal to the Fellow NPI (15436) within the same procedure, i.e., an individual cannot be the Operator and Fellow for the same procedure."
15431,Fellowship Program Identification Number,Fellow Information,FELLOW,Indicate the institution's Accreditation Council for Graduate Medical Education (ACGME) number for the program in which the Fellow is participating.,The value on current procedure,FITProgID,ST,15,,Single,,No,Report,Yes,,User,No,Yes,No,224873004,2.16.840.1.113883.6.96,SNOMED CT,
14840,Access System Counter,Access Systems,ACCESSSYS,The access system counter distinguishes an individual access system when multiple are used during one procedure.,The value on current procedure,AccessSysCounter,CTR,,,Single,Null,No,Illegal,Yes,,Automatic,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14839,Access System ID,Access Systems,ACCESSSYS,Indicate the access system(s) utilized during the current procedure.,The value on current procedure,AccessSysID,CD,,,Multiple,,Yes,Illegal,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,Access System ID (14839) cannot be Null when Procedure Canceled (14834) is No
14842,Device Counter,Devices,DEVICES,The device counter distinguishes individual devices when multiple are used during one procedure.,The value on current procedure,DevCounter,CTR,,,Single,Null,No,Report,Yes,,Automatic,No,Yes,No,2.16.840.1.113883.3.3478.4.851,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14841,Device ID,Devices,DEVICES,Indicate the device(s) utilized during the current procedure.,The value on current procedure,LAADevID,CD,,,Multiple,,Yes,Illegal,Yes,,User,No,Yes,No,63653004,2.16.840.1.113883.6.96,SNOMED CT,
14843,Device UDI Direct Identifier,Devices,DEVICES,"[Reserved for Future Use] Indicate the direct identifier portion of the Unique Device Identifier (UDI) associated with the device used. This ID is provided by the device manufacturer, and is either a GTIN or HIBBC number.",The value on current procedure,Dev_UDIDirectID,ST,150,,Single,,No,Report,Yes,,User,No,Yes,No,2.16.840.1.113883.3.3719,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14844,LAA Isolation Approach,Devices,DEVICES,Indicate which approach was used to deliver the closure device.,The value on current procedure,LAAIsolationApproach,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14968,Device Successfully Deployed,Devices,DEVICES,Indicate whether the device was successfully deployed.,The value on current procedure,DevSucdep,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1000142349,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"When Device ID (14841) is provided then Device Successfully Deployed (14968) cannot be Null.

If more than one Device Counter (14842) within the Procedure/Lab Visit have identical LAA Isolation Approach (14844) values, then only one Device Counter (14842) can have a Device Successfully Deployed (14968) = 'Yes'."
14845,Reason Device Not Deployed Successfully,Devices,DEVICES,Indicate the outcome listed for the device unsuccessfully deployed.,The value on current procedure,OutDevUnsucDepl,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,When Device Successfully Deployed (14968) is No then Outcome of Device Unsuccessfully Deployed (14845) cannot be Null
7210,Cumulative Air Kerma,Radiation Exposure,RADIATION,"Indicate the total radiation dose (Cumulative Air Kerma, or Reference Air Kerma) recorded to the nearest milligray (mGy) or gray (Gy). The value recorded should include the total dose for the lab visit. Cumulative air kerma is the total air kerma accrued from the beginning of an examination or procedure and includes all contributions from fluoroscopic and radiographic irradiation.",The total between start of current procedure and end of current procedure,FluoroDoseKerm,PQ,"5,0",mGy,Single,,No,Report,Yes,,User,No,Yes,No,228850003,2.16.840.1.113883.6.96,SNOMED CT,
7215,Contrast Volume,Radiation Exposure,RADIATION,Indicate the volume of contrast (ionic and non-ionic) used in milliliters (ml). The volume recorded should be the total volume for the lab visit.,The total between start of current procedure and end of current procedure,ContrastVol,PQ,"3,0",mL,Single,,No,Report,Yes,,User,No,Yes,No,80242-1,2.16.840.1.113883.6.1,LOINC,
14278,Dose Area Product,Radiation Exposure,RADIATION,Indicate the total fluoroscopy dose to the nearest integer. The value recorded should include the total dose for the lab visit.,The total between start of current procedure and end of current procedure,FluoroDoseDAP2,PQ,"7,0",Gy·cm²,Single,,No,Report,Yes,,User,No,Yes,No,100000994,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
7225,Intraprocedure Anticoagulation,Intraprocedure Anticoagulation Strategy,INTRAPROCANTICOAG,Indicate if intraprocedure anticoagulation therapy was provided.,The value on current procedure,IntraProcAnticoag,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,81839001,2.16.840.1.113883.6.96,SNOMED CT,
7230,Uninterrupted Warfarin Therapy,Intraprocedure Anticoagulation Strategy,INTRAPROCANTICOAG,Indicate if the patient continued on warfarin therapy and it was not held for the procedure. ,The value on current procedure,Warfarin,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100001238,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
15139,Heparin Administered During Procedure,Intraprocedure Anticoagulation Strategy,INTRAPROCANTICOAG,Indicate if heparin was administered during the procedure.,The value on current procedure,ProcHeparin2,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,84812008,2.16.840.1.113883.6.96,SNOMED CT,
14852,Heparin Initial Administration Timing,Intraprocedure Anticoagulation Strategy,INTRAPROCANTICOAG,Indicate the timing of initial administration of heparin.,Any occurrence on current procedure,ProcHeparinInitAdminTime,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,100001251,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
15140,Bivalirudin,Intraprocedure Anticoagulation Strategy,INTRAPROCANTICOAG,Indicate if bivalirudin was administered during the procedure.,The value on current procedure,ProcBivalirudin2,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,400610005,2.16.840.1.113883.6.96,SNOMED CT,
15138,Other Anticoagulant,Intraprocedure Anticoagulation Strategy,INTRAPROCANTICOAG,"Indicate if any other anticoagulation medical therapy, not listed above, was used during the procedure.",The value on current procedure,ProcOtherAnticoag2,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,100001064,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14853,Anticoagulation Reversal,Intraprocedure Anticoagulation Strategy,INTRAPROCANTICOAG,Indicate if there was a reversal of the anticoagulation at the end of the LAA occlusion procedure.,Any occurrence on current procedure,AnticoagReversal,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,413560001,2.16.840.1.113883.6.96,SNOMED CT,
12153,Intra or Post Procedure Events,Intra or Post-Procedure Events,IPPEVENTS,Indicate if the specific intra or post procedure event(s) occurred. ,Any occurrence between start of procedure and until next procedure or discharge,ProcEvents,CD,,,Single,,Yes,Report,Yes,,User,No,Yes,No,**********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"An Intra or Post Procedure - combination of Events (12153), Occurred (9002) and Event Date (14275) - may only be entered/selected once"
9002,Intra/Post-Procedure Events Occurred,Intra or Post-Procedure Events,IPPEVENTS,Indicate if the specific intra or post procedure event(s) occurred.,Any occurrence between start of procedure and until next procedure or discharge,PostProcOccurred,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1000142479,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"When an Intra or Post-Procedure Event (12153) selection/code is provided more than once in a single Lab Visit, then its Intra/Post-Procedure Events Occurred (9002) value cannot have conflicting responses or be duplicate negatives.

When an Intra or Post Procedure Events (12153) is selected then Intra/Post-Procedure Events Occurred (9002) must not be Null"
14275,Intra and Post Procedure Event Date,Intra or Post-Procedure Events,IPPEVENTS,Indicate all dates of intra or post procedure events that occurred.,Any occurrence between start of procedure and until next procedure or discharge,IntraPostProcEventDate,DT,,,Single,,No,Illegal,Yes,,User,No,Yes,No,**********0,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"Intra and Post Procedure Event Date (14275) must be Greater than or Equal to Procedure Start Date and Time (7000)

Intra and Post Procedure Event Date (14275) must be Less than or Equal to Discharge Date (10100)"
14312,Adjudication Event,In-Hospital Adjudication,HOSPEVEADJ,Indicate the event being adjudicated.,N/A,AJ_AdjudEvent,CD,,,Single,,Yes,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"An Adjudication - combination of Event (14312) and Date (14313) - may only be entered/selected once

Adjudication Event (14312) cannot be Null if Intra or Post Procedure Events (12153) is Equal to (Hemorrhagic Stroke, Intracranial Hemorrhage (other than hemorrhagic stroke), Ischemic Stroke, TIA, Undetermined Stroke, Access Site Bleeding, GI Bleeding, Hematoma, Hemothorax (not requiring drainage), Hemothorax (requiring drainage), Other Hemorrhage (non-intracranial), Pericardial Effusion (requiring open heart surgery), Pericardial Effusion with tamponade (requiring percutaneous drainage), Pericardial Effusion without tamponade (requiring percutaneous drainage), Retroperitoneal Bleeding, Vascular Complications, Systemic Thromboembolism (other than stroke), AV Fistula (requiring surgical repair), Pseudoaneurysm (requiring endovascular repair), Pseudoaneurysm (requiring surgical repair), Pseudoaneurysm (requiring thrombin injection only)) and Intra/Post-Procedure Events Occurred (9002) is Yes. Every Intra or Post Procedure Event (combination of Event (12153), Occurred (9002) and Event Date (14275)) that requires adjudication must have a corresponding adjudication record (combination of Event (14312) and Event Date (14313).

"
14313,Adjudication Event Date,In-Hospital Adjudication,HOSPEVEADJ,"Indicate the date the clinical event being adjudicated occurred. 
",N/A,AJ_EventDate,DT,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"When Adjudication Event (14312) is selected, Adjudication Event Date (14313) cannot be Null

The Adjudication Event Date (14313) / Adjudication Event Code (14312) must match with Intra or Post-Procedure Event Date (14275) / Intra or Post Procedure Event Code (12153)

Adjudication Event Date (14313) must be Less than or Equal to Discharge Date (10100)"
14902,Adjudication Status,Neurologic,NEURO,Indicate whether the patient was alive or deceased on the date the adjudication was performed.,Any value between start of current procedure and discharge,ADJ_NeuroAdjStatus,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"Adjudication Status (14902) cannot be Null if Adjudication Event (14312) is Equal to (Hemorrhagic Stroke, Intracranial Hemorrhage, Ischemic Stroke, TIA, Undetermined Stroke)"
14903,Adjudication Date of Death,Neurologic,NEURO,Indicate the date the patient was declared deceased.,Any value between start of current procedure and discharge,ADJ_DeathDate,DT,,,Single,,No,Report,Yes,,User,No,Yes,No,399753006,2.16.840.1.113883.6.96,SNOMED CT,"Adjudication Date of Death (14903) must be Greater than or Equal to Procedure Start Date and Time (7000)

Adjudication Date of Death (14903) must be Greater than or Equal to Adjudication Event Date (14313)

Adjudication Date of Death (14903) must be Greater than or Equal to Symptom Onset Date (14904)

Adjudication Date of Death (14903) must be Less than or Equal to Discharge Date (10100)"
14904,Symptom Onset Date,Neurologic,NEURO,Indicate the date of symptom onset associated with this event.,Any value between start of current procedure and discharge,ADJ_NeuroSxOnset,DT,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"Symptom Onset Date (14904) must be Greater than or Equal to Procedure Start Date and Time (7000)

Symptom Onset Date (14904) must be Less than or Equal to Discharge Date (10100)"
14905,Neurologic Deficit with Rapid Onset,Neurologic,NEURO,"Indicate if the patient had a sudden onset of a focal or global neurologic deficit regardless of the duration of symptoms. 
 
Rapid onset means sudden or maximal within minutes.
 ",Any value between start of current procedure and discharge,ADJ_NeuroDeficit,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,264552009,2.16.840.1.113883.6.96,SNOMED CT,
14906,Neurologic Deficit Clinical Presentation,Neurologic,NEURO,Indicate the clinical presentation of the neurologic deficit.,Any value between start of current procedure and discharge,ADJ_NeuroClinicPresent,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,264552009,2.16.840.1.113883.6.96,SNOMED CT,
14907,Diagnosis Confirmation by Neurology,Neurologic,NEURO,Indicate if the diagnosis was confirmed by a neurologist or a neurosurgeon.,Any value between start of current procedure and discharge,ADJ_NeuroDxConfirmed,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14908,Brain Imaging Performed,Neurologic,NEURO,"Indicate if neuro imaging (such as CT, MRI, cerebral angiography) was performed in an attempt to confirm the diagnosis.",All values between start of current procedure and discharge,ADJ_NeuroBrainImaging,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,441986001,2.16.840.1.113883.6.96,SNOMED CT,
14909,Brain Imaging Type,Neurologic,NEURO,Indicate the type of neurologic imaging which was performed.,All values between start of procedure and end of procedure,ADJ_NeuroBrainImagingType,CD,,,Multiple,,No,Report,Yes,,User,No,Yes,No,441986001,2.16.840.1.113883.6.96,SNOMED CT,
14910,Deficit Type,Neurologic,NEURO,Indicate the type of deficit identified by the neuroimaging study.,All values between start of procedure and end of procedure,ADJ_NeuroDeficitType,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14911,Hemorrhagic Stroke Type,Neurologic,NEURO,"For patients presenting with an intracranial hemorrhage, indicate the hemorrhage location.",All values between start of current procedure and discharge,ADJ_NeuroIntracranType,CD,,,Multiple,,No,Report,Yes,,User,No,Yes,No,230706003,2.16.840.1.113883.6.96,SNOMED CT,
14912,Subsequent Intravenous Recombinant Tissue Plasminogen Activator Administered,Neurologic,NEURO,Indicate if intravascular (IV) recombinant tissue plasminogen activator (rtPA) was used as a treatment option related to this event.,Any value between start of current procedure and discharge,ADJ_NeuroIVrTPA,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14913,Subsequent Endovascular Therapeutic Intervention,Neurologic,NEURO,Indicate if an endovascular interventional therapy was performed as a treatment option related to this event.,Any value between start of current procedure and discharge,ADJ_NeuroEndoTheraInter,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14914,Neurologic Symptoms Duration,Neurologic,NEURO,Indicate the duration (in hours) of the neurologic symptoms.,All values between start of procedure and end of procedure,ADJ_NeuroSxDuration,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,308921004,2.16.840.1.113883.6.96,SNOMED CT,
14915,Trauma,Neurologic,NEURO,Indicate if the patient experienced a physical trauma within 24 hours prior to the neurologic event.,Any value between start of current procedure and discharge,ADJ_NeuroTrauma,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,417746004,2.16.840.1.113883.6.96,SNOMED CT,
14916,Modified Rankin Scale,Neurologic,NEURO,Indicate the patient’s functional ability according to the modified Rankin Scale (mRS) administered following the event.,Any value between start of current procedure and discharge,ADJ_RankinScale,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,75859-9,2.16.840.1.113883.6.1,LOINC,
14917,Adjudication Modified Rankin Scale Not Administered,Neurologic,NEURO,Indicate if the modified Rankin Scale (mRS) was not administered following the event.,Any value between start of current procedure and discharge,ADJ_RankinScaleNA,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,75859-9,2.16.840.1.113883.6.1,LOINC,
14918,Procedure Related Neurologic Event,Neurologic,NEURO,Indicate using the following selections the likelihood in which this event is related to the LAAO procedure based upon the clinician's best clinical judgement.,Any value between start of current procedure and discharge,ADJ_NeuroProcRelated,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,405619009,2.16.840.1.113883.6.96,SNOMED CT,
14931,Device Related Neurologic Event,Neurologic,NEURODEV,Indicate using the following selections the likelihood in which this event is related to the LAAO device based upon the clinician's best clinical judgement.,Any value between start of current procedure and discharge,ADJ_NeuroDevRelated,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,405619009,2.16.840.1.113883.6.96,SNOMED CT,
14924,Adjudication Status,Bleeding,BLEED,Indicate whether the patient was alive or deceased on the date the adjudication was performed.,Any value between start of current procedure and discharge,ADJ_BleedAdjStatus,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"Adjudication Status (14924) cannot be Null if Adjudication Event (14312) is Equal to (Access Site Bleeding, GI Bleeding, Hematoma, Hemothorax (not requiring drainage), Hemothorax (requiring drainage), Other Hemorrhage, Pericardial Effusion (requiring open cardiac surgery), Pericardial Effusion with tamponade (requiring percutaneous drainage), Pericardial Effusion without tamponade (requiring percutaneous drainage), Retroperitoneal Bleeding, Vascular Complications, AV Fistula (requiring surgical repair), Pseudoaneurysm (requiring endovascular repair), Pseudoaneurysm (requiring surgical repair), Pseudoaneurysm (requiring thrombin injection only))"
14930,Adjudication Date of Death,Bleeding,BLEED,Indicate the date the patient was declared deceased.,Any value between start of current procedure and discharge,ADJ_BleedDeathDate,DT,,,Single,,No,Report,Yes,,User,No,Yes,No,399753006,2.16.840.1.113883.6.96,SNOMED CT,"Adjudication Date of Death (14930) must be Greater than or Equal to Procedure Start Date and Time (7000)

Adjudication Date of Death (14930) must be Greater than or Equal to Adjudication Event Date (14313)"
14929,Invasive Intervention Required,Bleeding,BLEED,Indicate if there was a surgical or percutaneous intervention required to treat the patient for this bleeding event.,Any value between start of current procedure and discharge,ADJ_BleedInvInter,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14919,RBC Transfusion,Bleeding,BLEED,Indicate if there was at least one transfusion of PRBCs given to treat the patient for this bleeding event.,All values between start of current procedure and discharge,ADJ_BleedRBCTransfusion,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,71493000,2.16.840.1.113883.6.96,SNOMED CT,
14920,Number of RBC Units Transfused,Bleeding,BLEED,Indicate the number of PRBC units transfused for treatment of this bleeding event.,All values between start of current procedure and discharge,ADJ_BleedRBCUnits,PQ,"3,1",unit,Single,,No,Report,Yes,,User,No,Yes,No,100014031,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14921,Hemoglobin Pre-Transfusion,Bleeding,BLEED,"Indicate the lowest hemoglobin (Hgb) value (g/dL), obtained via lab assay or point of care assay, between the intra or post procedure bleeding event and prior to the transfusion.",All values between start of current procedure and discharge,ADJ_BleedPreTransHgb,PQ,"3,1",g/dL,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14922,Diagnostic Imaging Performed,Bleeding,BLEED,"Indicate if imaging (such as CT, MRI) was performed in an attempt to confirm the diagnosis.",All values between start of current procedure and discharge,ADJ_BleedImagePerf,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14923,End Organ Damage,Bleeding,BLEED,Indicate if the patient was diagnosed with end organ damage after this bleeding event.,All values between start of procedure and end of procedure,ADJ_BleedEndOrganDamage,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14927,Major Surgery,Bleeding,BLEED,Indicate if the patient underwent surgery within 30 days prior to this bleeding event.,Any value between start of current procedure and discharge,ADJ_BleedMajorSurgery,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14928,Percutaneous Coronary Intervention,Bleeding,BLEED,Indicate if the patient had a percutaneous coronary artery or percutaneous valvular intervention within 30 days prior to this bleeding event.,Any value between start of current procedure and discharge,ADJ_BleedPCI,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,415070008,2.16.840.1.113883.6.96,SNOMED CT,
14925,Procedure Related Bleeding Event,Bleeding,BLEED,Indicate using the following selections the likelihood in which this event is related to the LAAO procedure based upon the clinician's best clinical judgement.,Any value between start of current procedure and discharge,ADJ_BleedProcRelated,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,131148009,2.16.840.1.113883.6.96,SNOMED CT,
14926,Device Related Bleeding Event,Bleeding,BLEEDDEV,Indicate using the following selections the likelihood in which this event is related to the LAAO device based upon the clinician's best clinical judgement.,Any value between start of current procedure and discharge,ADJ_BleedDevRelated,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,131148009,2.16.840.1.113883.6.96,SNOMED CT,
14932,Adjudication Status,Systemic Thromboembolism,SYSTHROMB,Indicate whether the patient was alive or deceased on the date the adjudication was performed.,Any value between start of current procedure and discharge,ADJ_SysThromboAdjStatus,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,Adjudication Status (14932) cannot be Null if Adjudication Event (14312) is Equal to (Systemic Thromboembolism (other than stroke))
14933,Adjudication Date of Death,Systemic Thromboembolism,SYSTHROMB,Indicate the date the patient was declared deceased.,Any value between start of current procedure and discharge,ADJ_SysThromboDeathDate,DT,,,Single,,No,Report,Yes,,User,No,Yes,No,399753006,2.16.840.1.113883.6.96,SNOMED CT,"Adjudication Date of Death (14933) must be Greater than or Equal to Procedure Start Date and Time (7000)

Adjudication Date of Death (14933) must be Greater than or Equal to Adjudication Event Date (14313)"
14934,Cause of Death,Systemic Thromboembolism,SYSTHROMB,"If deceased, indicate if the patient's death cause was due to systemic thromboembolization, or focal end-organ hypoperfusion resulting from systemic thromboembolism, or therapeutic intervention to treat systemic thromboembolism.",Any value between start of current procedure and discharge,ADJ_SysThromboDeathCause,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,184305005,2.16.840.1.113883.6.96,SNOMED CT,
14935,Focal End-Organ Hypoperfusion Present,Systemic Thromboembolism,SYSTHROMB,Indicate if focal end-organ hypoperfusion resulted from the systemic thromboembolism event.,Any value between start of current procedure and discharge,ADJ_SysThromboHypoperfusion,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14939,Systemic Thromboembolization Imaging Evidence,Systemic Thromboembolism,SYSTHROMB,Indicate if imaging evidence indicated systemic thromboembolism.,All values between start of procedure and end of procedure,ADJ_SysThromboImagEvidence,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,365853002,2.16.840.1.113883.6.96,SNOMED CT,
14936,Imaging Method,Systemic Thromboembolism,SYSTHROMB,Indicate the imaging method to identify systemic thromboembolism.,All values between start of current procedure and discharge,ADJ_SysThromboImagMethod,CD,,,Multiple,,No,Report,Yes,,User,No,Yes,No,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14937,Therapeutic Intervention Performed,Systemic Thromboembolism,SYSTHROMB,"Indicate if any pharmacological, catheter, surgical, or other therapeutic intervention was performed to treat the systemic thromboembolism.",All values between start of procedure and end of procedure,ADJ_SysThromboTheraInterv,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100013063,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14938,Intervention Type,Systemic Thromboembolism,SYSTHROMB,Indicate the intervention type.,All values between start of procedure and end of procedure,ADJ_SysThromboIntervType,CD,,,Multiple,,No,Report,Yes,,User,No,Yes,No,100013063,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14940,Adjudication Medication Code,In-Hospital Adjudication Medications,ADJMEDS,Indicate the NCDR assigned identification number for the medications the patient was taking or administered at the time of the event.,All values between start of current procedure and discharge,ADJ_MedID,CD,,,Single,,Yes,Report,Yes,,User,No,Yes,No,100013057,2.16.840.1.113883.3.3478.6.1,ACC NCDR,Adjudication Medication Code (14940) should not be duplicated within an adjudication event
14941,Medication Administered,In-Hospital Adjudication Medications,ADJMEDS,Indicate if the patient was taking or being administered the medication at the time of the event.,All values between start of procedure and end of procedure,ADJ_MedAdmin,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,432102000,2.16.840.1.113883.6.96,SNOMED CT,"When an Adjudication Medication Code (14940) is selected, Medication Administered (14941) cannot be Null"
14868,Post Procedure Peak Creatinine,Post Procedure Labs,POSTPROCLABS,Indicate the post-procedure peak creatinine (Cr) level (mg/dL).,The highest value between end of current procedure and discharge,PostProcPeakCreat,PQ,"4,2",mg/dL,Single,,No,Report,Yes,,User,No,Yes,No,2160-0,2.16.840.1.113883.6.1,LOINC,
14870,Post Procedure Peak Creatinine Not Drawn,Post Procedure Labs,POSTPROCLABS,Indicate if post-procedure peak creatinine level could not be assessed as either only one level or no creatinine labs were drawn.,N/A,PostProcPeakCreatND,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,2160-0,2.16.840.1.113883.6.1,LOINC,
14871,Post Procedure Hemoglobin,Post Procedure Labs,POSTPROCLABS,"Indicate the lowest hemoglobin (Hgb) value (g/dL), obtained via lab assay or point of care assay, between the end of the procedure and the time of discharge.",The lowest value between end of current procedure and discharge,PostProcHgb2,PQ,"3,1",g/dL,Single,,No,Report,Yes,,User,No,Yes,No,718-7,2.16.840.1.113883.6.1,LOINC,
14872,Post Procedure Hemoglobin Not Drawn,Post Procedure Labs,POSTPROCLABS,Indicate if the post-procedure hemoglobin was not drawn.,N/A,PostProcHgbND2,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,718-7,2.16.840.1.113883.6.1,LOINC,
14869,Post Procedure Creatinine,Post Procedure Creatinine,POSTPROCCRT,Indicate the post-procedure creatinine (Cr) level (mg/dL).,The last value between end of last procedure and discharge,PostProcCreat2,PQ,"4,2",mg/dL,Single,,No,Report,Yes,,User,No,Yes,No,2160-0,2.16.840.1.113883.6.1,LOINC,
14867,Post Procedure Creatinine Not Drawn,Post Procedure Creatinine,POSTPROCCRT,Indicate if the post-procedure creatinine level was not drawn.,N/A,PostProcCreatND2,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,2160-0,2.16.840.1.113883.6.1,LOINC,
14835,Surgery,Discharge,DISCHARGE,Indicate if the patient had an inpatient operation during this episode of care.,The value on discharge,Sx_F,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,387713003,2.16.840.1.113883.6.96,SNOMED CT,
14836,Percutaneous Coronary Intervention,Discharge,DISCHARGE,"Indicate if the patient had any other percutaneous coronary artery, coronary valvular or coronary structural interventions during this episode of care.",The value on discharge,PCIOther,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,415070008,2.16.840.1.113883.6.96,SNOMED CT,
10100,Discharge Date,Discharge,DISCHARGE,Indicate the date on which the patient was discharged from your facility.,The value on discharge,DCDate,DT,,,Single,Null,No,Illegal,Yes,,User,No,Yes,No,**********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"Discharge Date (10100) and Arrival Date (3000) must not overlap on multiple episodes

Discharge Date (10100) must be Greater than or Equal to 10/01/2022"
10105,Discharge Status,Discharge,DISCHARGE,Indicate whether the patient was alive or deceased at discharge.,The value on discharge,DCStatus,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,75527-2,2.16.840.1.113883.6.1,LOINC,
10110,Discharge Location,Discharge,DISCHARGE,Indicate the location to which the patient was discharged.,The value on discharge,DCLocation,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,75528-0,2.16.840.1.113883.6.1,LOINC,
10115,Hospice Care,Discharge,DISCHARGE,Indicate if the patient was discharged to hospice care.,The value on discharge,DCHospice,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,385763009,2.16.840.1.113883.6.96,SNOMED CT,
10120,Death During the Procedure,Discharge,DISCHARGE,"Indicate if the patient expired during the procedure.

Note(s): Make sure to only capture 'death during the procedure' in the procedure appropriate registry. 
 
For example, if the patient had a CathPCI procedure and a TVT procedure in the same episode of care (hospitalization) but different cath lab visits and the death occurred during the TVT procedure, code 'Yes' only in the TVT Registry and not the CathPCI Registry.  If the CathPCI procedure and TVT procedure occurred during the same cath lab visit then code 'Yes' in both registries.",Any occurrence on discharge,DeathProcedure,BL,,,Single,,No,Report,Yes,,User,No,Yes,No,100000923,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
10125,Cause of Death,Discharge,DISCHARGE,"Indicate the primary cause of death, i.e. the first significant abnormal event which ultimately led to death.",The value on time of death,DeathCause,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,184305005,2.16.840.1.113883.6.96,SNOMED CT,"When Cause of Death (10125) is Equal to Stroke, at least one of the following Adjudication Events (14312) must be selected: Hemorrhagic Stroke, Intracranial Hemorrhage, Ischemic Stroke, TIA, Undetermined Stroke

When Cause of Death (10125) is Equal to (Cardiovascular hemorrhage, Hemorrhage), at least one of the following Adjudication Events (14312) must be selected: Access Site Bleeding, GI Bleeding, Hematoma, Hemothorax (not requiring drainage), Hemothorax (requiring drainage), Other Hemorrhage, Pericardial Effusion (requiring open cardiac surgery), Pericardial Effusion with tamponade (requiring percutaneous drainage), Pericardial Effusion without tamponade (requiring percutaneous drainage), Retroperitoneal Bleeding, Vascular Complications, AV Fistula (requiring surgical repair), Pseudoaneurysm (requiring endovascular repair), Pseudoaneurysm (requiring surgical repair), Pseudoaneurysm (requiring thrombin injection only)"
10200,Discharge Medication Code,Discharge Medications,DCMEDS,"Indicate the assigned identification number associated with the medications the patient was prescribed upon discharge.

Note(s):
Discharge medications not required for patients who expired, discharged to ""Other acute care hospital"", ""Left against medical advice (AMA)"" or are receiving Hospice Care.

The medication(s) collected in this field are controlled by the Medication Master file. This file is maintained by the NCDR and will be made available on the internet for downloading and importing/updating into your application. Each medication in the Medication Master file is assigned to a value set. The value set is used to separate procedural medications from medications prescribed at discharge. The separation of these medications is depicted on the data collection form.",N/A,DC_MedID,CD,,,Single,,Yes,Report,Yes,,User,No,Yes,No,100013057,2.16.840.1.113883.3.3478.6.1,ACC NCDR,Discharge Medication Code (10200) should not be duplicated in an episode
10205,Discharge Medication Prescribed,Discharge Medications,DCMEDS,"Indicate if the medication was prescribed, not prescribed, or was not prescribed for either a medical or patient reason.",The value on discharge,DC_MedAdmin,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,432102000,2.16.840.1.113883.6.96,SNOMED CT,
10207,Discharge Medication Dose,Discharge Medications,DCMEDS,Indicate the category of the  medication dose prescribed.,The value on discharge,DC_MedDose,CD,,,Single,,No,Report,Yes,,User,No,Yes,No,100014233,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
10999,Follow-Up Unique Key,Follow-Up,FOLLOWUP,Indicate the unique key associated with each patient follow-up record as assigned by the EMR/EHR or your software application.,N/A,FollowUpKey,ST,50,,Single,,No,Illegal,Yes,,Automatic,Yes,No,Yes,**********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
11000,Follow-Up Assessment Date,Follow-Up,FOLLOWUP,Indicate the date of the follow-up assessment was performed.,The value on Follow-up,F_AssessmentDate,DT,,,Single,,No,Illegal,Yes,,User,No,No,Yes,**********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"Follow-Up Assessment Date (11000) must be Greater than the Follow-Up Reference Procedure Start Date and Time (11001)

Follow-Up Assessment Date (11000) must be Greater than or Equal to 10/01/2022"
14851,Follow Up Interval,Follow-Up,FOLLOWUP,"Indicate the interval of follow-up: 45 days, 6 months, 1 year or 2 years.",The value on Follow-up,FUInterv,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"A Follow Up - combination of Follow Up Interval (14851), Follow-Up Assessment Date (11000) and Follow-Up Reference Procedure Start Date and Time (11001) - may only be entered/selected once

The date difference between Follow-up Assessment Date (11000) and Follow-up Reference Procedure Start Date and Time (11001) must fall within the valid range for the Follow Up Interval (14851). The valid ranges for the Follow Up Interval (14851) selections are as follows: 

45 day: 1-91 days
6 month: 92-256 days
1 year: 257-548 days
2 year: 549-913 days"
14946,Reference Episode Arrival Date,Follow-Up,FOLLOWUP,Indicate the date and time of arrival for the episode of care that included the reference procedure.,The value on Follow-up,FURefArrivalDate,DT,,,Single,,No,Illegal,Yes,,User,No,No,Yes,1000142436,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14338,Follow-Up Reference Discharge Date,Follow-Up,FOLLOWUP,Indicate the date of discharge for the episode of care that included the reference procedure.,The value on Follow-up,FU_RefDischargeDate,DT,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,Follow-up Reference Discharge Date (14338) must not be Null
11001,Follow-Up Reference Procedure Start Date and Time,Follow-Up,FOLLOWUP,Indicate the reference procedure start date and time on the follow-up assessment date.,The value on Follow-up,RefProcStartDateTime,TS,,,Single,,No,Illegal,Yes,,User,No,No,Yes,**********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
11003,Method to Determine Follow-Up Status,Follow-Up,FOLLOWUP,Indicate the method(s) used to determine the patient's vital status for follow up.,The value on Follow-up,F_Method,CD,,,Multiple,,No,Report,Yes,,User,No,No,Yes,100014059,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
11004,Follow-Up Status,Follow-Up,FOLLOWUP,Indicate whether the patient is alive or deceased.,The value on Follow-up,F_Status,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,308273005,2.16.840.1.113883.6.96,SNOMED CT,
11006,Follow-Up Date of Death,Follow-Up,FOLLOWUP,Indicate the date of death.,The value on Follow-up,F_DeathDate,DT,,,Single,,No,Report,Yes,,User,No,No,Yes,**********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"Follow-Up Date of Death (11006) must be Greater than the Follow-Up Reference Procedure Start Date and Time (11001)

Follow-Up Date of Death (11006) must be Greater than or Equal to the Follow-Up Event Date (14277)"
11007,Cause of Death,Follow-Up,FOLLOWUP,"Indicate the primary cause of death, i.e. the first significant abnormal event which ultimately led to death.",The value on Follow-up,F_DeathCause,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,184305005,2.16.840.1.113883.6.96,SNOMED CT,"When Cause of Death (11007) is Equal to Stroke, at least one of the following Adjudication Events (14967) must be selected: Hemorrhagic Stroke, Intracranial Hemorrhage, Ischemic Stroke, TIA, Undetermined Stroke

When Cause of Death (11007) is Equal to (Cardiovascular hemorrhage, Hemorrhage), at least one of the following Adjudication Events (14967) must be selected: Access Site Bleeding, GI Bleeding, Hematoma, Hemothorax (not requiring drainage), Hemothorax (requiring drainage), Other Hemorrhage, Pericardial Effusion (requiring open cardiac surgery), Pericardial Effusion with tamponade (requiring percutaneous drainage), Pericardial Effusion without tamponade (requiring percutaneous drainage), Retroperitoneal Bleeding, Vascular Complications, AV Fistula (requiring surgical repair), Pseudoaneurysm (requiring endovascular repair), Pseudoaneurysm (requiring surgical repair), Pseudoaneurysm (requiring thrombin injection only)"
14858,Left Ventricular Ejection Fraction Assessed,Follow-Up,FOLLOWUP,Indicate if a left ventricular ejection fraction (LVEF) has been assessed.,The value on Follow-up,FU_LVEF,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,100001027,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
13690,Left Ventricular Ejection Fraction,Follow-Up,FOLLOWUP,"Indicate the left ventricular ejection fraction.
",The value on Follow-up,F_LVEF,PQ,"2,0",%,Single,,No,Report,Yes,,User,No,No,Yes,10230-1,2.16.840.1.113883.6.1,LOINC,
14859,Transthoracic Echo Performed,Follow-Up,FOLLOWUP,Indicate if a transthoracic echocardiogram (TTE) was performed.,The value on Follow-up,TTEPerfFU,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,433236007,2.16.840.1.113883.6.96,SNOMED CT,
14873,TTE Date,Follow-Up,FOLLOWUP,Indicate the date of the most recent transthoracic echo study performed.,The value on Follow-up,TTEDate_F,DT,,,Single,,No,Report,Yes,,User,No,No,Yes,433236007,2.16.840.1.113883.6.96,SNOMED CT,TTE Date (14873) must be Greater than the Follow-Up Reference Procedure Start Date and Time (11001)
14874,Transesophageal Echocardiogram (TEE) Performed,Follow-Up,FOLLOWUP,Indicate if transesophageal echocardiogram (TEE) was performed.,The value on Follow-up,F_TEEPerf,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,105376000,2.16.840.1.113883.6.96,SNOMED CT,
14875,TEE Date,Follow-Up,FOLLOWUP,Indicate the date of the most recent transesophageal echocardiogram (TEE).,The value on Follow-up,F_TEEDate,DT,,,Single,,No,Report,Yes,,User,No,No,Yes,105376000,2.16.840.1.113883.6.96,SNOMED CT,TEE Date (14875) must be Greater than the Follow-Up Reference Procedure Start Date and Time (11001)
14876,Cardiac CT Performed,Follow-Up,FOLLOWUP,Indicate if cardiac computed tomography (CT) was performed.,The value on Follow-up,F_CardiacCTPerf,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,58744-4,2.16.840.1.113883.6.1,LOINC,
14877,Cardiac CT Date,Follow-Up,FOLLOWUP,Indicate the date of the most recent cardiac computed tomography (CT).,The value on Follow-up,F_CardiacCTDate,DT,,,Single,,No,Report,Yes,,User,No,No,Yes,58744-4,2.16.840.1.113883.6.1,LOINC,Cardiac CT Date (14877) must be Greater than the Follow-Up Reference Procedure Start Date and Time (11001)
14878,Cardiac MRI Performed,Follow-Up,FOLLOWUP,Indicate if cardiac magnetic resonance imaging (MRI) was performed.,The value on Follow-up,F_CardiacMRIPerf,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,36482-8,2.16.840.1.113883.6.1,LOINC,
14879,Cardiac MRI Date,Follow-Up,FOLLOWUP,Indicate the date of the most recent cardiac magnetic resonance imaging (MRI).,The value on Follow-up,F_CardiacMRIDate,DT,,,Single,,No,Report,Yes,,User,No,No,Yes,36482-8,2.16.840.1.113883.6.1,LOINC,Cardiac MRI Date (14879) must be Greater than the Follow-Up Reference Procedure Start Date and Time (11001)
14880,Intracardiac Echo Performed,Follow-Up,FOLLOWUP,Indicate if intracardiac echo (ICE) was performed.,The value on Follow-up,F_ICEPerformed,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,448761005,2.16.840.1.113883.6.96,SNOMED CT,
14881,Date of Intracardiac Echo,Follow-Up,FOLLOWUP,Indicate the date of the most recent intracardiac echo (ICE).,The value on Follow-up,F_ICEDate,DT,,,Single,,No,Report,Yes,,User,No,No,Yes,448761005,2.16.840.1.113883.6.96,SNOMED CT,
14882,Atrial Thrombus Detected,Follow-Up,FOLLOWUP,Indicate if a left atrial thrombus was detected.,The value on Follow-up,F_AtrialThromDetect,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,396339007:123005000=59652004,2.16.840.1.113883.6.96,SNOMED CT,
14884,Device Margin Residual Leak,Follow-Up,FOLLOWUP,Indicate the residual leak at the device margin in millimeters (mm).,The value on Follow-up,ResidualLeakFU,PQ,"3,1",mm,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14885,Device Margin Residual Leak Not Assessed,Follow-Up,FOLLOWUP,Indicate if the residual leak at the device margin was not assessed.,The value on Follow-up,ResidualLeakNAFU,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14886,Creatinine,Follow-Up,FOLLOWUP,Indicate the most recent creatinine (Cr) level (mg/dL).,The value on Follow-up,Creat_FU,PQ,"4,2",mg/dL,Single,,No,Report,Yes,,User,No,No,Yes,2160-0,2.16.840.1.113883.6.1,LOINC,
14887,Creatinine Not Drawn,Follow-Up,FOLLOWUP,Indicate if the creatinine level was not drawn. ,The value on Follow-up,F_CreatND,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,2160-0,2.16.840.1.113883.6.1,LOINC,
14888,Lowest Hemoglobin Value,Follow-Up,FOLLOWUP,"Indicate the lowest hemoglobin (Hgb) value (g/dL), obtained via lab assay or point of care assay, after discharge if 45 day follow up; or the lowest value between the prior follow up visit and current follow up visit for any follow up after the 45 day follow up.",The value on Follow-up,LowHgbValue_F,PQ,"4,2",g/dL,Single,,No,Report,Yes,,User,No,No,Yes,718-7,2.16.840.1.113883.6.1,LOINC,
14889,Hemoglobin Not Drawn,Follow-Up,FOLLOWUP,Indicate if the hemoglobin was not drawn.,The value on Follow-up,HGBND_FU,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,718-7,2.16.840.1.113883.6.1,LOINC,
13148,Modified Rankin Scale Score,Follow-Up,FOLLOWUP,Indicate the Modified Rankin Scale score.,The value on Follow-up,F_RankinScore,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,75859-9,2.16.840.1.113883.6.1,LOINC,
14890,Modified Rankin Scale Not Administered,Follow-Up,FOLLOWUP,Indicate if the Modified Rankin Scale was not administered at follow-up.,The value on Follow-up,F_mRS_NA,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,75859-9,2.16.840.1.113883.6.1,LOINC,
14891,Barthel Index Evaluation Performed,Follow-Up,FOLLOWUP,Indicate if the Barthel Index Evaluation was performed. The Barthel Index (BI) or Barthel ADL index is a scale used to measure performance in basic activities of daily living (ADL).,The value on Follow-up,F_BIEPerf,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,96762-0,2.16.840.1.113883.6.1,LOINC,
14892,Feeding,Follow-Up,FOLLOWUP,"Choose the scoring point for the statement that most closely corresponds to the patient's current level of ability. Record actual, not potential, functioning. Information can be obtained from the patient's self-report, from a separate party who is familiar with the patient's abilities (such as a relative), or from observation.",The value on Follow-up,F_BIEFeeding,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,83184-2,2.16.840.1.113883.6.1,LOINC,
14893,Bathing,Follow-Up,FOLLOWUP,"Choose the scoring point for the statement that most closely corresponds to the patient's current level of ability. Record actual, not potential, functioning. Information can be obtained from the patient's self-report, from a separate party who is familiar with the patient's abilities (such as a relative), or from observation.",The value on Follow-up,F_BIEBathing,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,83181-8,2.16.840.1.113883.6.1,LOINC,
14894,Grooming,Follow-Up,FOLLOWUP,"Choose the scoring point for the statement that most closely corresponds to the patient's current level of ability. Record actual, not potential, functioning. Information can be obtained from the patient's self-report, from a separate party who is familiar with the patient's abilities (such as a relative), or from observation.",The value on Follow-up,F_BIEGrooming,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,96767-9,2.16.840.1.113883.6.1,LOINC,
14895,Dressing,Follow-Up,FOLLOWUP,"Choose the scoring point for the statement that most closely corresponds to the patient's current level of ability. Record actual, not potential, functioning. Information can be obtained from the patient's self-report, from a separate party who is familiar with the patient's abilities (such as a relative), or from observation.",The value on Follow-up,F_BIEDressing,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,83182-6,2.16.840.1.113883.6.1,LOINC,
14896,Bowels,Follow-Up,FOLLOWUP,"Choose the scoring point for the statement that most closely corresponds to the patient's current level of ability. Record actual, not potential, functioning. Information can be obtained from the patient's self-report, from a separate party who is familiar with the patient's abilities (such as a relative), or from observation.",The value on Follow-up,F_BIEBowels,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,96759-6,2.16.840.1.113883.6.1,LOINC,
14897,Bladder,Follow-Up,FOLLOWUP,"Choose the scoring point for the statement that most closely corresponds to the patient's current level of ability. Record actual, not potential, functioning. Information can be obtained from the patient's self-report, from a separate party who is familiar with the patient's abilities (such as a relative), or from observation.",The value on Follow-up,F_BIEBladder,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,96760-4,2.16.840.1.113883.6.1,LOINC,
14898,Toilet,Follow-Up,FOLLOWUP,"Choose the scoring point for the statement that most closely corresponds to the patient's current level of ability. Record actual, not potential, functioning. Information can be obtained from the patient's self-report, from a separate party who is familiar with the patient's abilities (such as a relative), or from observation.",The value on Follow-up,F_BIEToilet,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,83183-4,2.16.840.1.113883.6.1,LOINC,
14899,Transfers,Follow-Up,FOLLOWUP,"Choose the scoring point for the statement that most closely corresponds to the patient's current level of ability. Record actual, not potential, functioning. Information can be obtained from the patient's self-report, from a separate party who is familiar with the patient's abilities (such as a relative), or from observation.",The value on Follow-up,F_BIETransfers,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,83185-9,2.16.840.1.113883.6.1,LOINC,
14900,Mobility,Follow-Up,FOLLOWUP,"Choose the scoring point for the statement that most closely corresponds to the patient's current level of ability. Record actual, not potential, functioning. Information can be obtained from the patient's self-report, from a separate party who is familiar with the patient's abilities (such as a relative), or from observation.",The value on Follow-up,F_BIEMobility,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,83186-7,2.16.840.1.113883.6.1,LOINC,
14901,Stairs,Follow-Up,FOLLOWUP,"Choose the scoring point for the statement that most closely corresponds to the patient's current level of ability. Record actual, not potential, functioning. Information can be obtained from the patient's self-report, from a separate party who is familiar with the patient's abilities (such as a relative), or from observation.",The value on Follow-up,F_BIEStairs,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,96758-8,2.16.840.1.113883.6.1,LOINC,
11990,Follow-Up Medications Code,Follow-Up Medications,FUPMEDS,"Indicate the assigned identification number associated with the medications the patient was prescribed or received.

Note(s):
The medication(s) collected in this field are controlled by the Medication Master file. This file is maintained by the NCDR and will be made available on the internet for downloading and importing/updating into your application. Each medication in the Medication Master file is assigned to a value set. The value set is used to separate procedural medications from medications prescribed at discharge. The separation of these medications is depicted on the data collection form.",N/A,F_MedID,CD,,,Single,,Yes,Report,Yes,,User,No,No,Yes,100013057,2.16.840.1.113883.3.3478.6.1,ACC NCDR,Follow-Up Medications Code (11990) should not be duplicated in a follow-up
14949,Follow-up Current Medications at Time of Follow-up,Follow-Up Medications,FUPMEDS,"Indicate if the patient was taking or being administered the medication at the time of follow-up, or was not taking or being administered the medication for an undocumented, a medical, or a patient reason.",The value on Follow-up,F_MedAdmin2,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,432102000,2.16.840.1.113883.6.96,SNOMED CT,
14950,Follow-Up Medication Dose,Follow-Up Medications,FUPMEDS,Indicate the category of the medication dose.,The value on Follow-up,F_MedDose2,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,100014233,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14951,Follow-up Warfarin Discontinued,Follow-Up Anticoagulation Therapy,FUPANTICOAG,Indicate if the patient discontinued Warfarin at any time since the last follow-up (or since discharge if this is the 45-day follow-up).,The value on Follow-up,F_WarfarinDiscontinued,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14952,Follow-up Warfarin Discontinued Date,Follow-Up Anticoagulation Therapy,FUPANTICOAG,Indicate the date the Warfarin was discontinued.,The value on Follow-up,F_WarfarinDiscontinuedDate,DT,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,Follow-up Warfarin Discontinued Date (14952) must be Greater than the Follow-Up Reference Procedure Start Date and Time (11001)
14953,Follow-up Warfarin Resumed,Follow-Up Anticoagulation Therapy,FUPANTICOAG,Indicate if the patient resumed Warfarin at any time since the last follow-up (or since discharge if this is the 45-day follow-up).,The value on Follow-up,F_WarfarinResumed,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14954,Follow-up Warfarin Resumed Date,Follow-Up Anticoagulation Therapy,FUPANTICOAG,Indicate the date the Warfarin was resumed.,The value on Follow-up,F_WarfarinResumedDate,DT,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,Follow-up Warfarin Resumed Date (14954) must be Greater than the Follow-Up Reference Procedure Start Date and Time (11001)
14955,Follow-up DOAC Therapy Discontinued,Follow-Up Anticoagulation Therapy,FUPANTICOAG,Indicate if the patient discontinued Direct Oral Anticoagulant (DOAC) Therapy at any time since the last follow-up (or since discharge if this is the 45-day follow-up).,The value on Follow-up,F_DOACTherapyDiscontinued,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14956,Follow-up DOAC Therapy Discontinued Date,Follow-Up Anticoagulation Therapy,FUPANTICOAG,Indicate the date the DOAC was discontinued.,The value on Follow-up,F_DOACTherapyDiscontinuedDate,DT,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,Follow-up NOAC (DOAC) Therapy Discontinued Date (14956) must be Greater than the Follow-Up Reference Procedure Start Date and Time (11001)
14957,Follow-up DOAC Therapy Resumed,Follow-Up Anticoagulation Therapy,FUPANTICOAG,Indicate if the patient resumed Direct Oral Anticoagulant (DOAC) Therapy at any time since the last follow-up (or since discharge if this is the 45-day follow-up).,The value on Follow-up,F_DOACTherapyResumed,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14958,Follow-up DOAC Therapy Resumed Date,Follow-Up Anticoagulation Therapy,FUPANTICOAG,Indicate the date the DOAC was resumed.,The value on Follow-up,F_DOACTherapyResumedDate,DT,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,Follow-up NOAC (DOAC) Therapy Resumed Date (14958) must be Greater than the Follow-Up Reference Procedure Start Date and Time (11001)
14959,Follow-up Aspirin Therapy Discontinued,Follow-Up Anticoagulation Therapy,FUPANTICOAG,Indicate if the patient discontinued Aspirin Therapy at any time since the last follow-up (or since discharge if this is the 45-day follow-up).,The value on Follow-up,F_AspirinTherapyDiscontinued,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14960,Follow-up Aspirin Therapy Discontinued Date,Follow-Up Anticoagulation Therapy,FUPANTICOAG,Indicate the date the Aspirin was discontinued.,The value on Follow-up,F_AspirinTherapyDiscontinuedDate,DT,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,Follow-up Aspirin Therapy Discontinued Date (14960) must be Greater than the Follow-Up Reference Procedure Start Date and Time (11001)
14961,Follow-up Aspirin Therapy Resumed,Follow-Up Anticoagulation Therapy,FUPANTICOAG,Indicate if the patient resumed Aspirin Therapy at any time since the last follow-up (or since discharge if this is the 45-day follow-up).,The value on Follow-up,F_AspirinTherapyResumed,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14962,Follow-up Aspirin Therapy Resumed Date,Follow-Up Anticoagulation Therapy,FUPANTICOAG,Indicate the date the Aspirin was resumed.,The value on Follow-up,F_AspirinTherapyResumedDate,DT,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,Follow-up Aspirin Therapy Resumed Date (14962) must be Greater than the Follow-Up Reference Procedure Start Date and Time (11001)
14963,Follow-up P2Y12 Therapy Discontinued,Follow-Up Anticoagulation Therapy,FUPANTICOAG,Indicate if the patient discontinued P2Y12 Therapy at any time since the last follow-up (or since discharge if this is the 45-day follow-up).,The value on Follow-up,F_P2Y12TherapyDiscontinued,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14964,Follow-up P2Y12 Therapy Discontinued Date,Follow-Up Anticoagulation Therapy,FUPANTICOAG,Indicate the date the P2Y12 was discontinued.,The value on Follow-up,F_P2Y12TherapyDiscontinuedDate,DT,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,Follow-up P2Y12 Therapy Discontinued Date (14964) must be Greater than the Follow-Up Reference Procedure Start Date and Time (11001)
14965,Follow-up P2Y12 Therapy Resumed,Follow-Up Anticoagulation Therapy,FUPANTICOAG,Indicate if the patient resumed P2Y12 Therapy at any time since the last follow-up (or since discharge if this is the 45-day follow-up).,The value on Follow-up,F_P2Y12TherapyResumed,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14966,Follow-up P2Y12 Therapy Resumed Date,Follow-Up Anticoagulation Therapy,FUPANTICOAG,Indicate the date the P2Y12 was resumed.,The value on Follow-up,F_P2Y12TherapyResumedDate,DT,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,Follow-up P2Y12 Therapy Resumed Date (14966) must be Greater than the Follow-Up Reference Procedure Start Date and Time (11001)
14948,Follow-Up Event,Follow-Up Events,FUPEVENTS,Indicate if any event from the NCDR-provided list had occurred between the time of the last follow-up (or discharge if this is the 45-day follow-up) and the current follow-up.,The value on Follow-up,F_Event,CD,,,Single,,Yes,Report,Yes,,User,No,No,Yes,**********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"A Follow-Up - combination of Event Name (14948), Occurred (14276) and Date (14277) - may only be entered/selected once"
14276,Follow-Up Events Occurred,Follow-Up Events,FUPEVENTS,Indicate if the event occurred.,Any occurrence on follow-up,FupEvOccurred,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,1000142378,2.16.840.1.113883.3.3478.6.1,ACC NCDR,When a Follow-Up Event (14948) is provided then Follow-Up Events Occurred (14276) cannot be Null
14277,Follow-Up Event Date,Follow-Up Events,FUPEVENTS,Indicate the date the event occurred.,Any occurrence on follow-up,FupEventDate,DT,,,Single,,No,Illegal,Yes,,User,No,No,Yes,1000142379,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"Follow-Up Event Date (14277) must be Less than or Equal to the Follow-Up Assessment Date (11000)

Follow-Up Event Date (14277) must be Greater than the Follow-Up Reference Procedure Start Date and Time (11001)"
14967,Adjudication Event,Follow-Up Adjudication,FADJ,Indicate the event being adjudicated.,The value on Follow-up,F_Adj_AdjudEvent,CD,,,Single,,Yes,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"An Adjudication - combination of Event (14967) and Date (14386) - may only be entered/selected once

Adjudication Event (14967) cannot be Null if Follow-Up Event (14948) is Equal to (Hemorrhagic Stroke, Intracranial Hemorrhage (other than hemorrhagic stroke), Ischemic Stroke, TIA, Undetermined Stroke, Access Site Bleeding, GI Bleeding, Hematoma, Hemothorax (not requiring drainage), Hemothorax (requiring drainage), Other Hemorrhage (non-intracranial), Pericardial Effusion (requiring open heart surgery), Pericardial Effusion with tamponade (requiring percutaneous drainage), Pericardial Effusion without tamponade (requiring percutaneous drainage), Retroperitoneal Bleeding, Vascular Complications, Systemic Thromboembolism (other than stroke), AV Fistula (requiring surgical repair), Pseudoaneurysm (requiring endovascular repair), Pseudoaneurysm (requiring surgical repair), Pseudoaneurysm (requiring thrombin injection only)) and Follow-Up Events Occurred (14276) is Yes. Every Follow-up Event (combination of Event (14948), Occurred (14276) and Event Date (14277)) that requires adjudication must have a corresponding adjudication record (combination of Event (14967) and Event Date (14386)."
14386,Adjudication Event Date,Follow-Up Adjudication,FADJ,Indicate the date the clinical event being adjudicated occurred. ,N/A,F_AJ_EventDate,DT,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"When Adjudication Event (14967) is selected, Adjudication Event Date (14386) cannot be Null

The Adjudication Event Date (14386) / Adjudication Event (14967) must match with Follow-Up Event Date (14277) / Follow-Up Event (14948)"
14969,Adjudication Status,Follow-Up Neurologic,FNEURO,Indicate whether the patient was alive or deceased on the date the adjudication was performed.,Any value between discharge or last follow up and the current follow up,F_ADJ_NeuroAdjStatus,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"Adjudication Status (14969) cannot be Null if Follow-Up Adjudication Event (14967) is Equal to (Hemorrhagic Stroke, Intracranial Hemorrhage, Ischemic Stroke, TIA, Undetermined Stroke)"
14970,Adjudication Date of Death,Follow-Up Neurologic,FNEURO,Indicate the date the patient was declared deceased.,Any value between discharge or last follow up and the current follow up,F_ADJ_DeathDate,DT,,,Single,,No,Report,Yes,,User,No,No,Yes,399753006,2.16.840.1.113883.6.96,SNOMED CT,"Adjudication Date of Death (14970) must be Greater than Follow-Up Reference Procedure Start Date and Time (11001)

Adjudication Date of Death (14970) must be Greater than or Equal to Follow-Up Adjudication Event Date (14386)

Adjudication Date of Death (14970) must be Greater than or Equal to Follow-Up Symptom Onset Date (14976)"
14976,Symptom Onset Date,Follow-Up Neurologic,FNEURO,Indicate the date of symptom onset associated with this event.,Any value between discharge or last follow up and the current follow up,FU_ADJ_NeuroSxOnset,DT,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"Follow-Up Symptom Onset Date (14976) must be Greater than or Equal to Follow-Up Reference Discharge Date (14338)

Follow-Up Symptom Onset Date (14976) must be Greater than or Equal to Follow-Up Reference Procedure Start Date Time (11001)

Follow-up Symptom Onset Date (14976) must be Less than or Equal to Follow-up Adjudication Event Date (14386)"
14977,Neurologic Deficit with Rapid Onset,Follow-Up Neurologic,FNEURO,"Indicate if the patient had a sudden onset of a focal or global neurologic deficit regardless of the duration of symptoms. 
 
Rapid onset means sudden or maximal within minutes.
 ",Any value between discharge or last follow up and the current follow up,FU_ADJ_NeuroDeficit,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,264552009,2.16.840.1.113883.6.96,SNOMED CT,
14978,Neurologic Deficit Clinical Presentation,Follow-Up Neurologic,FNEURO,Indicate the clinical presentation of the neurologic deficit.,Any value between discharge or last follow up and the current follow up,FU_ADJ_NeuroClinicPresent,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,264552009,2.16.840.1.113883.6.96,SNOMED CT,
14979,Diagnosis Confirmation by Neurology,Follow-Up Neurologic,FNEURO,Indicate if the diagnosis was confirmed by a neurologist or a neurosurgeon.,Any value between discharge or last follow up and the current follow up,FU_ADJ_NeuroDxConfirmed,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14980,Brain Imaging Performed,Follow-Up Neurologic,FNEURO,"Indicate if neuro imaging (such as CT, MRI, cerebral angiography) was performed in an attempt to confirm the diagnosis.",All values between discharge or last follow up and the current follow up,FU_ADJ_NeuroBrainImaging,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,441986001,2.16.840.1.113883.6.96,SNOMED CT,
14981,Brain Imaging Type,Follow-Up Neurologic,FNEURO,Indicate the type of neurologic imaging which was performed.,All values between discharge or last follow up and the current follow up,FU_ADJ_NeuroBrainImagingType,CD,,,Multiple,,No,Report,Yes,,User,No,No,Yes,441986001,2.16.840.1.113883.6.96,SNOMED CT,
14982,Deficit Type,Follow-Up Neurologic,FNEURO,Indicate the type of deficit identified by the neuroimaging study.,All values between discharge or last follow up and the current follow up,FU_ADJ_NeuroDeficitType,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14983,Hemorrhagic Stroke Type,Follow-Up Neurologic,FNEURO,"For patients presenting with an intracranial hemorrhage, indicate the hemorrhage location.",All values between discharge or last follow up and the current follow up,FU_ADJ_NeuroIntracranType,CD,,,Multiple,,No,Report,Yes,,User,No,No,Yes,230706003,2.16.840.1.113883.6.96,SNOMED CT,
14984,Subsequent Intravenous Recombinant Tissue Plasminogen Activator Administered,Follow-Up Neurologic,FNEURO,Indicate if intravascular (IV) recombinant tissue plasminogen activator (rtPA) was used as a treatment option related to this event.,Any value between discharge or last follow up and the current follow up,FU_ADJ_NeuroIVrTPA,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14985,Subsequent Endovascular Therapeutic Intervention,Follow-Up Neurologic,FNEURO,Indicate if an endovascular interventional therapy was performed as a treatment option related to this event.,Any value between discharge or last follow up and the current follow up,FU_ADJ_NeuroEndoTheraInter,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14986,Neurologic Symptoms Duration,Follow-Up Neurologic,FNEURO,Indicate the duration (in hours) of the neurologic symptoms.,All values between discharge or last follow up and the current follow up,FU_ADJ_NeuroSxDuration,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,308921004,2.16.840.1.113883.6.96,SNOMED CT,
14987,Trauma,Follow-Up Neurologic,FNEURO,Indicate if the patient experienced a physical trauma within 24 hours prior to the neurologic event.,Any value between discharge or last follow up and the current follow up,FU_ADJ_NeuroTrauma,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,417746004,2.16.840.1.113883.6.96,SNOMED CT,
14988,Modified Rankin Scale,Follow-Up Neurologic,FNEURO,Indicate the patient’s functional ability according to the modified Rankin Scale (mRS) administered following the event.,Any value between discharge or last follow up and the current follow up,FU_ADJ_RankinScale,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,75859-9,2.16.840.1.113883.6.1,LOINC,
14989,Adjudication Modified Rankin Scale Not Administered,Follow-Up Neurologic,FNEURO,Indicate if the modified Rankin Scale (mRS) was not administered following the event.,Any value between discharge or last follow up and the current follow up,FU_ADJ_RankinScaleNA,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,75859-9,2.16.840.1.113883.6.1,LOINC,
14990,Procedure Related Neurologic Event,Follow-Up Neurologic,FNEURO,Indicate using the following selections the likelihood in which this event is related to the LAAO procedure based upon the clinician's best clinical judgement.,All values between discharge or last follow up and the current follow up,FU_ADJ_NeuroProcRelated,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,405619009,2.16.840.1.113883.6.96,SNOMED CT,
15015,Follow-up Device Related Neurologic Event,Follow-Up Neurologic,FNEURODEV,Indicate using the following selections the likelihood in which this event is related to the LAAO device based upon the clinician's best clinical judgement.,Any value between discharge or last follow up and the current follow up,F_ADJ_NeuroDevRelated,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,405619009,2.16.840.1.113883.6.96,SNOMED CT,
14971,Adjudication Status,Follow-Up Bleeding,FBLEED,Indicate whether the patient was alive or deceased on the date the adjudication was performed.,Any value between discharge or last follow up and the current follow up,F_ADJ_BleedAdjStatus,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,"Adjudication Status (14971) cannot be Null if Adjudication Event (14967) is Equal to (Access Site Bleeding, GI Bleeding, Hematoma, Hemothorax (not requiring drainage), Hemothorax (requiring drainage), Other Hemorrhage, Pericardial Effusion (requiring open cardiac surgery), Pericardial Effusion with tamponade (requiring percutaneous drainage), Pericardial Effusion without tamponade (requiring percutaneous drainage), Retroperitoneal Bleeding, Vascular Complications), AV Fistula (requiring surgical repair), Pseudoaneurysm (requiring endovascular repair), Pseudoaneurysm (requiring surgical repair), Pseudoaneurysm (requiring thrombin injection only)"
14972,Adjudication Date of Death,Follow-Up Bleeding,FBLEED,Indicate the date the patient was declared deceased.,Any value between discharge or last follow up and the current follow up,FU_ADJ_BleedDeathDate,DT,,,Single,,No,Report,Yes,,User,No,No,Yes,399753006,2.16.840.1.113883.6.96,SNOMED CT,"Adjudication Date of Death (14972) must be Greater than the Follow-Up Reference Procedure Start Date and Time (11001)

Adjudication Date of Death (14972) must be Greater than or Equal to Follow-Up Adjudication Event Date (14386)"
14991,Invasive Intervention Required,Follow-Up Bleeding,FBLEED,Indicate if there was a surgical or percutaneous intervention required to treat the patient for this bleeding event.,Any value between discharge or last follow up and the current follow up,FU_ADJ_BleedInvInter,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14992,RBC Transfusion,Follow-Up Bleeding,FBLEED,Indicate if there was at least one transfusion of PRBCs given to treat the patient for this bleeding event.,All values between discharge or last follow up and the current follow up,FU_ADJ_BleedRBCTransfusion,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,71493000,2.16.840.1.113883.6.96,SNOMED CT,
14993,Follow-up number of RBC Units Transfused,Follow-Up Bleeding,FBLEED,Indicate the number of PRBC units transfused for treatment of this bleeding event.,All values between discharge or last follow up and the current follow up,FU_ADJ_BleedRBCUnits,PQ,"3,1",unit,Single,,No,Report,Yes,,User,No,No,Yes,100014031,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14994,Hemoglobin Pre-Transfusion,Follow-Up Bleeding,FBLEED,"Indicate the lowest hemoglobin (Hgb) value (g/dL), obtained via lab assay or point of care assay, between the intra or post procedure bleeding event and prior to the transfusion.",All values between discharge or last follow up and the current follow up,FU_ADJ_BleedPreTransHgb,PQ,"3,1",g/dL,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14995,Diagnostic Imaging Performed,Follow-Up Bleeding,FBLEED,"Indicate if imaging (such as CT, MRI) was performed in an attempt to confirm the diagnosis.",All values between discharge or last follow up and the current follow up,FU_ADJ_BleedImagePerf,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14996,End Organ Damage,Follow-Up Bleeding,FBLEED,Indicate if the patient was diagnosed with end organ damage after this bleeding event.,All values between discharge or last follow up and the current follow up,FU_ADJ_BleedEndOrganDamage,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14975,Bleeding Event Readmission,Follow-Up Bleeding,FBLEED,Indicate if a readmission was associated with a bleeding related diagnosis.,Any value between discharge or last follow up and the current follow up,FU_BleedReadm,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14997,Major Surgery,Follow-Up Bleeding,FBLEED,Indicate if the patient underwent surgery within 30 days prior to this bleeding event.,Any value between discharge or last follow up and the current follow up,FU_ADJ_BleedMajorSurgery,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
14998,Percutaneous Coronary Intervention,Follow-Up Bleeding,FBLEED,Indicate if the patient had a percutaneous coronary artery or percutaneous valvular intervention within 30 days prior to this bleeding event.,Any value between discharge or last follow up and the current follow up,FU_ADJ_BleedPCI,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,415070008,2.16.840.1.113883.6.96,SNOMED CT,
14999,Procedure Related Bleeding Event,Follow-Up Bleeding,FBLEED,Indicate using the following selections the likelihood in which this event is related to the LAAO procedure based upon the clinician's best clinical judgement.,Any value between discharge or last follow up and the current follow up,FU_ADJ_BleedProcRelated,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,131148009,2.16.840.1.113883.6.96,SNOMED CT,
15000,Device Related Bleeding Event,Follow-Up Bleeding,FBLEEDDEV,Indicate using the following selections the likelihood in which this event is related to the LAAO device based upon the clinician's best clinical judgement.,Any value between discharge or last follow up and the current follow up,FU_ADJ_BleedDevRelated,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,131148009,2.16.840.1.113883.6.96,SNOMED CT,
14973,Adjudication Status,Follow-Up Systemic Thromboembolism,FSYSTHROMB,Indicate whether the patient was alive or deceased on the date the adjudication was performed.,Any value between discharge or last follow up and the current follow up,FU_ADJ_SysThromboAdjStatus,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,Adjudication Status (14973) cannot be Null if Adjudication Event (14967) is Equal to (Systemic Thromboembolism (other than stroke))
14974,Adjudication Date of Death,Follow-Up Systemic Thromboembolism,FSYSTHROMB,Indicate the date the patient was declared deceased.,Any value between discharge or last follow up and the current follow up,F_ADJ_SysThromboDeathDate,DT,,,Single,,No,Report,Yes,,User,No,No,Yes,399753006,2.16.840.1.113883.6.96,SNOMED CT,"Adjudication Date of Death (14974) must be Greater than the Follow-Up Reference Procedure Start Date and Time (11001)

Adjudication Date of Death (14974) must be Greater than or Equal to the Follow-Up Adjudication Event Date (14386)"
15016,Death Cause (End-Organ Hypoperfusion OR Systemic Thromboembolization OR Intervention),Follow-Up Systemic Thromboembolism,FSYSTHROMB,"If deceased, indicate if the patient's death cause was due to systemic thromboembolization, or focal end-organ hypoperfusion resulting from systemic thromboembolism, or therapeutic intervention to treat systemic thromboembolism.",Any value between discharge or last follow up and the current follow up,F_ADJ_SysThromboDeathCause,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,184305005,2.16.840.1.113883.6.96,SNOMED CT,
15001,Focal End-Organ Hypoperfusion Present,Follow-Up Systemic Thromboembolism,FSYSTHROMB,Indicate if focal end-organ hypoperfusion resulted from the systemic thromboembolism event.,Any value between discharge or last follow up and the current follow up,FU_ADJ_SysThromboHypoperfusion,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
15002,Systemic Thromboembolization Imaging Evidence,Follow-Up Systemic Thromboembolism,FSYSTHROMB,Indicate if imaging evidence indicated systemic thromboembolism.,All values between discharge or last follow up and the current follow up,FU_ADJ_SysThromboImagEvidence,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,365853002,2.16.840.1.113883.6.96,SNOMED CT,
15003,Follow-up Imaging Method,Follow-Up Systemic Thromboembolism,FSYSTHROMB,Indicate the imaging method to identify systemic thromboembolism.,All values between discharge or last follow up and the current follow up,FU_ADJ_SysThromboImagMethod,CD,,,Multiple,,No,Report,Yes,,User,No,No,Yes,1.12E+11,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
15004,Therapeutic Intervention Performed,Follow-Up Systemic Thromboembolism,FSYSTHROMB,"Indicate if any pharmacological, catheter, surgical, or other therapeutic intervention was performed to treat the systemic thromboembolism.",All values between discharge or last follow up and the current follow up,FU_ADJ_SysThromboTheraInterv,BL,,,Single,,No,Report,Yes,,User,No,No,Yes,100013063,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
15005,Intervention Type,Follow-Up Systemic Thromboembolism,FSYSTHROMB,Indicate the intervention type.,All values between discharge or last follow up and the current follow up,FU_ADJ_SysThromboIntervType,CD,,,Multiple,,No,Report,Yes,,User,No,No,Yes,100013063,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
15006,Adjudication Medication Code,Follow-Up Adjudication Medications,FADJMEDS,Indicate the NCDR assigned identification number for the medications the patient was taking or administered at the time of the event.,All values between discharge or last follow up and the current follow up,FU_ADJ_MedID,CD,,,Single,,Yes,Report,Yes,,User,No,No,Yes,100013057,2.16.840.1.113883.3.3478.6.1,ACC NCDR,Adjudication Medication Code (15006) should not be duplicated within an adjudication event
15007,Medication Administered,Follow-Up Adjudication Medications,FADJMEDS,Indicate if the patient was taking or being administered the medication at the time of the event.,All values between discharge or last follow up and the current follow up,FU_ADJ_MedAdmin,CD,,,Single,,No,Report,Yes,,User,No,No,Yes,432102000,2.16.840.1.113883.6.96,SNOMED CT,"When an Adjudication Medication Code (15006) is selected, Medication Administered (15007) cannot be Null"
1000,Participant ID,Administration,ADMIN,Indicate the participant ID of the submitting facility.,N/A,PartID,NUM,8,,Single,Null,No,Illegal,Yes,,Automatic,No,Yes,Yes,2.16.840.1.113883.3.3478.4.836,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
1010,Participant Name,Administration,ADMIN,"Indicate the full name of the facility where the procedure was performed.

Note(s):
Values should be full, official hospital names with no abbreviations or variations in spelling.",N/A,PartName,ST,100,,Single,Null,No,Illegal,Yes,,Automatic,No,Yes,Yes,2.16.840.1.113883.3.3478.4.836,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
1020,Time Frame of Data Submission,Administration,ADMIN,"Indicate the time frame of data included in the data submission. Format: YYYYQQ. e.g.,2016Q1",N/A,Timeframe,ST,6,,Single,Null,No,Illegal,Yes,,Automatic,No,Yes,Yes,*******.4.1.19376.*******.5.45,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
1040,Transmission Number,Administration,ADMIN,"This is a unique number created, and automatically inserted by the software into export file. It identifies the number of times the software has created a data submission file. The transmission number should be incremented by one every time the data submission files are exported. The transmission number should never be repeated.",N/A,XmsnId,NUM,9,,Single,Null,No,Illegal,Yes,,Automatic,No,Yes,Yes,*******.4.1.19376.*******.5.45,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
1050,Vendor Identifier,Administration,ADMIN,Vendor identification (agreed upon by mutual selection between the vendor and the NCDR) to identify software vendor. This is entered into the schema automatically by vendor software. Vendors must use consistent name identification across sites. Changes to vendor name identification must be approved by the NCDR.,N/A,VendorId,ST,15,,Single,Null,No,Illegal,Yes,,Automatic,No,Yes,Yes,2.16.840.1.113883.3.3478.4.840,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
1060,Vendor Software Version,Administration,ADMIN,Vendor's software product name and version number identifying the software which created this record (assigned by vendor). Vendor controls the value in this field. This is entered into the schema automatically by vendor software.,N/A,VendorVer,ST,20,,Single,Null,No,Illegal,Yes,,Automatic,No,Yes,Yes,2.16.840.1.113883.3.3478.4.847,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
1070,Registry Identifier,Administration,ADMIN,The NCDR registry identifier describes the data registry to which these records apply. It is implemented in the software at the time the data is collected and records are created. This is entered into the schema automatically by software.,N/A,RegistryId,ST,30,,Single,ACC-NCDR-LAAO-1.4,No,Illegal,Yes,,Automatic,No,Yes,Yes,2.16.840.1.113883.3.3478.4.841,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
1071,Registry Schema Version,Administration,ADMIN,Schema version describes the version number of the Registry Transmission Document (RTD) schema to which each record conforms. It is an attribute that includes a constant value indicating the version of schema file. This is entered into the schema automatically by software.,N/A,SchemaVersion,NUM,"3,1",,Single,1,No,Illegal,Yes,,Automatic,No,Yes,Yes,**********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
1085,Submission Type,Administration,ADMIN,"Indicate if the data contained in the harvest/data file contains either standard patient episode of care records (arrival date to discharge only) or if it contains patient follow-up records. 

A transmission file with all episode of care records (from Arrival to Discharge only) is considered a 'Base Registry Record'.

A file with patient follow-up records (any follow-up assessments performed during the quarter selected) is considered a 'Follow-Up Record'.

Note(s):
Selecting 'Follow-Up Records Only' will transmit all patient records with Follow-up Assessment Dates (Element Ref# 11000) contained in the selected timeframe, regardless of the procedure or discharge date. For example, if a patient has a procedure on 3/30/2017, is discharged on 3/31/2017, and has a follow-up assessment on 5/6/2017, the patient's episode of care data will be transmitted in the 2017Q1 Base Registry Record file, but the Follow-up data will be transmitted in the 2017Q2 Follow-Up File.",N/A,SubmissionType,CD,,,Single,Null,No,Illegal,Yes,,Automatic,No,Yes,Yes,**********,2.16.840.1.113883.3.3478.6.1,ACC NCDR,
