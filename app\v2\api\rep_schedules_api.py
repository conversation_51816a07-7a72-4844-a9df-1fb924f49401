from fastapi import status, APIRouter, Depends, Response, Request, HTTPException
from v2.schema import (
    ScheduleCaseParams,
    ResponseSchema,
    RepSchedules,
    User,
    RepCaseListSchema,
    RepPatientCaseDetailSchema,
    CoordinatorPatientCaseDetailSchema,
    PatientUnassignedCaseList,
    ResponseDictSchema,
    ClinicianPatientCaseDetailSchema,
    PreOpRepUpdate,
)
from v2.utils import (
    api_response,
    authorizer,
    get_cha2ds2_vasc_score,
    get_has_bled_risk_score,
    get_ambra_image_url,
    get_patient_details_by_case_id,
    embedded_data_points_agent,
    get_case_details,
    create_review_tee,
    review_and_upload_truplan,
    update_assigned_to_for_cases,
)
import logging
from pymongo.database import Database, DBRef
from datetime import datetime, timezone
from bson import ObjectId
from v2.constants import (
    REPROLE,
    COORDROLE,
    AFIB_CLASSIFICATION,
    LAB_EKG_RHYTHM,
    REP_ADVISOR,
    DOSING_FREQUENCY,
    ANTICOAGULANT,
    CLINICIAN_ROLE,
    CUSTOM_MEDICINES_ORDER,
    PRIOR_ABLATION,
    RATIONALE,
)


router = APIRouter(prefix="/reps/schedule", tags=["V2 Rep Schedule"])


@router.get("/cases", response_model=ResponseSchema[RepSchedules])
async def get_rep_schedule_cases(
    request: Request,
    response: Response,
    queryparam: ScheduleCaseParams = Depends(),
    token_data: dict = Depends(authorizer),
):
    try:
        user_data = User(**token_data.get("data", {}))
        mongo_client: Database = request.app.database
        collection = mongo_client.get_collection("case_summary")

        pipeline = [
            # Join case_summary with SiteModel
            {
                "$lookup": {
                    "from": "site",
                    "localField": "site_id.$id",
                    "foreignField": "_id",
                    "as": "site_info",
                }
            },
            # Unwind site data
            {"$unwind": "$site_info"},
            # Join case_summary with implanting_physicians
            {
                "$lookup": {
                    "from": "implanting_physicians",
                    "localField": "implanting_physician_id.$id",
                    "foreignField": "_id",
                    "as": "physician_info",
                }
            },
            # Unwind implanting_physicians data
            {"$unwind": "$physician_info"},
            # Join case_summary with user
            {
                "$lookup": {
                    "from": "user",
                    "localField": "rep_id.$id",
                    "foreignField": "_id",
                    "as": "user_info",
                }
            },
            # Unwind user data
            {"$unwind": "$user_info"},
            # Match query conditions
            {
                "$match": {
                    "procedure_date": {
                        "$gte": queryparam.start_date,
                        "$lte": queryparam.end_date,
                    },
                    "user_info.keycloak_id": user_data.sub,
                    "deleted_at": None,
                    "physician_info.deleted_at": None,
                }
            },
            # Grouping
            {
                "$group": {
                    "_id": {
                        "site_id": "$site_id",
                        "implanting_physician_id": "$implanting_physician_id",
                        "procedure_date": "$procedure_date",
                    },
                    "site": {
                        "$first": {
                            "id": "$site_info._id",
                            "name": "$site_info.name",
                            "image_url": "$site_info.image_url",
                        }
                    },
                    "implanting_physician": {
                        "$first": {
                            "id": "$physician_info._id",
                            "name": {
                                "$concat": [
                                    "$physician_info.first_name",
                                    " ",
                                    "$physician_info.last_name",
                                    " ",
                                    {"$ifNull": ["$physician_info.credential", ""]},
                                ]
                            },
                            "image_url": "$physician_info.image_url",
                        }
                    },
                    "procedure_date": {"$first": "$procedure_date"},
                    "start_time": {"$min": "$procedure_time"},
                    "cases": {"$sum": 1},
                }
            },
            # Sorting by procedure_date
            {"$sort": {"procedure_date": 1}},
        ]
        data = collection.aggregate(pipeline).to_list()
        response.status_code = status.HTTP_200_OK
        if data:
            return api_response(data, "Cases fetched successfully", "success")
        else:
            return api_response(data, "No cases found", "success")

    except Exception:
        raise


@router.get(
    "/sites/{site_id}/cases/{case_date}",
    response_model=ResponseSchema[RepCaseListSchema],
)
async def get_case_details_by_site_and_case_date(
    request: Request,
    response: Response,
    site_id: str,
    case_date: str,
    token_data: dict = Depends(authorizer),
):
    try:
        user_data = User(**token_data.get("data", {}))

        mongo_client: Database = request.app.database
        collection = mongo_client.get_collection("case_summary")

        pipeline = [
            {
                "$match": {
                    "site_id.$id": ObjectId(site_id),
                    "procedure_date": case_date,
                    "deleted_at": None,
                }
            },
            {
                "$lookup": {
                    "from": "user",
                    "localField": "rep_id.$id",
                    "foreignField": "_id",
                    "as": "rep_info",
                }
            },
            {"$unwind": {"path": "$rep_info", "preserveNullAndEmptyArrays": False}},
            {"$match": {"rep_info.keycloak_id": user_data.sub}},
            {
                "$lookup": {
                    "from": "medicines",
                    "localField": "anticoagulant.medicine.$id",
                    "foreignField": "_id",
                    "as": "anticoagulant_info",
                }
            },
            # {
            #     "$match": {
            #         "anticoagulant_info.deleted_at": None,
            #     }
            # },
            {
                "$lookup": {
                    "from": "patient",
                    "localField": "patient_id.$id",
                    "foreignField": "_id",
                    "as": "patient_info",
                }
            },
            {"$unwind": {"path": "$patient_info", "preserveNullAndEmptyArrays": False}},
            {
                "$lookup": {
                    "from": "referring_provider",
                    "localField": "patient_info.referring_providers.$id",
                    "foreignField": "_id",
                    "as": "ref_provider",
                }
            },
            {"$unwind": {"path": "$ref_provider", "preserveNullAndEmptyArrays": True}},
            {
                "$lookup": {
                    "from": "laao_string_selections",
                    "localField": "rationale.$id",
                    "foreignField": "_id",
                    "as": "rationale_info",
                }
            },
            {
                "$unwind": {
                    "path": "$rationale_info",
                    "preserveNullAndEmptyArrays": True,
                }
            },
            {
                "$match": {
                    "rationale_info.deleted_at": None,
                }
            },
            {
                "$project": {
                    "_id": 0,
                    "case_id": "$_id",
                    "procedure_date": "$procedure_date",
                    "procedure_time": "$procedure_time",
                    "patient": {
                        "id": "$patient_info._id",
                        "name": {
                            "$concat": [
                                "$patient_info.first_name",
                                " ",
                                "$patient_info.last_name",
                            ]
                        },
                        "age": {
                            "$subtract": [
                                {"$year": datetime.now(timezone.utc)},  # Current year
                                {
                                    "$year": {
                                        "$dateFromString": {
                                            "dateString": "$patient_info.dob"
                                        }
                                    }
                                },  # Year of DOB
                            ]
                        },
                        "sex": "$patient_info.sex",
                        "cta": "$cta",
                        "tee": "$tee",
                        "afib_ablation": "$afib_ablation",
                        "rationale": {"$ifNull": ["$rationale_info.name", ""]},
                        "referring_provider": {
                            "id": "$ref_provider._id",
                            "name": {
                                "$concat": [
                                    "$ref_provider.first_name",
                                    " ",
                                    "$ref_provider.last_name",
                                    ", ",
                                    "$credential_info.name",
                                ]
                            },
                        },
                        "anticoagulation": {
                            "$map": {
                                "input": "$anticoagulant_info",
                                "as": "item",
                                "in": "$$item.name",
                            }
                        },
                    },
                }
            },
            {"$sort": {"procedure_date": 1, "procedure_time": 1}},
        ]

        data = collection.aggregate(pipeline).to_list()
        response.status_code = status.HTTP_200_OK
        if not data:
            return api_response([], "No cases found", "success")

        # Append CHA2DS2-VASc score for each case
        res_data = ""
        for case in data:
            patient_id = case.get("patient", {}).get("id")
            case_id = case.get("case_id")
            score = get_cha2ds2_vasc_score(case_id, mongo_client)

            ambra_params = {
                "filter.patientid.equals": str(patient_id),
                "filter.modality.equals": "CT",
            }

            case.get("patient", {}).update(
                {
                    "cha2ds2_vasc": score,
                    "study": get_ambra_image_url(ambra_params, mongo_client),
                }
            )

        return api_response(data, "Case list fetched", "success")

    except Exception:
        raise


@router.put("/cases/{case_id}", response_model=ResponseSchema)
async def assign_rep_to_specific_case(
    request: Request,
    response: Response,
    case_id: str,
    token_data: dict = Depends(authorizer),
):
    try:
        user_data = User(**token_data.get("data", {}))
        mongo_client: Database = request.app.database
        rep_collection = mongo_client.get_collection("user")
        collection = mongo_client.get_collection("case_summary")
        rep_detail = rep_collection.find_one({"keycloak_id": user_data.sub})
        if not (rep_detail):
            raise HTTPException(
                status.HTTP_400_BAD_REQUEST, "Not an rep or user not found"
            )
        res = collection.update_one(
            {"_id": ObjectId(case_id)},
            {"$set": {"rep_id": DBRef("user", ObjectId(rep_detail["_id"]))}},
        )
        if res.matched_count <= 0:
            raise HTTPException(status.HTTP_400_BAD_REQUEST, "case not found")

        embedded_data_points_agent(case_id, token_data["token"])

        try:
            # Reassign the task to the reps
            task_collection = mongo_client.get_collection("task_details")
            task_sub_type = mongo_client.get_collection("task_sub_type")
            update_assigned_to_for_cases(
                task_collection, task_sub_type, [case_id], str(rep_detail["_id"])
            )

            case_values = get_case_details(collection, case_id)

            check_tee = case_values["tee"]
            cta_image = case_values["cta_image"]
            truplan_uplaod_status = case_values["truplan_upload_status"]

            if check_tee:
                create_review_tee(mongo_client, case_id)
            review_and_upload_truplan(
                mongo_client, case_id, cta_image, truplan_uplaod_status
            )
        except Exception as e:
            logging.error(f"Task assigning failed {str(e)}")

        response.status_code = status.HTTP_200_OK
        return api_response([], "Case summary updated", "success")

    except Exception:
        raise


@router.get(
    "/cases/{case_id}/patient",
    response_model=ResponseDictSchema[
        RepPatientCaseDetailSchema
        | CoordinatorPatientCaseDetailSchema
        | ClinicianPatientCaseDetailSchema
    ],
)
async def get_specific_case_patient_record(
    request: Request,
    response: Response,
    case_id: str,
    token_data: dict = Depends(authorizer),
):
    try:
        user_data = User(**token_data.get("data", {}))
        mongo_client: Database = request.app.database
        collection = mongo_client.get_collection("case_summary")
        user_collection = mongo_client.get_collection("user")
        user_db_data = user_collection.find_one({"keycloak_id": user_data.sub})
        current_date = datetime.now(timezone.utc).date()
        current_datetime = datetime.combine(
            current_date, datetime.min.time(), tzinfo=timezone.utc
        )
        pipeline = [
            {"$match": {"_id": ObjectId(case_id), "deleted_at": None}},
            {"$unwind": {"path": "$anticoagulant", "preserveNullAndEmptyArrays": True}},
            {
                "$lookup": {
                    "from": "medicines",
                    "let": {
                        "medicine_id": "$anticoagulant.medicine.$id",
                        "quantity": "$anticoagulant.quantity",
                    },
                    "pipeline": [
                        {"$match": {"$expr": {"$eq": ["$_id", "$$medicine_id"]}}},
                        {"$addFields": {"quantity_val": "$$quantity"}},
                    ],
                    "as": "medicine_details",
                }
            },
            {
                "$unwind": {
                    "path": "$medicine_details",
                    "preserveNullAndEmptyArrays": True,
                }
            },
            {
                "$match": {
                    "medicine_details.deleted_at": None,
                }
            },
            {
                "$lookup": {
                    "from": "laao_string_selections",
                    "localField": "prior_ablation.$id",
                    "foreignField": "_id",
                    "as": "prior_ablation_info",
                }
            },
            {
                "$unwind": {
                    "path": "$prior_ablation_info",
                    "preserveNullAndEmptyArrays": True,
                }
            },
            {
                "$match": {
                    "prior_ablation_info.deleted_at": None,
                }
            },
            {
                "$lookup": {
                    "from": "laao_string_selections",
                    "pipeline": [
                        {
                            "$match": {
                                "deleted_at": None,
                            }
                        },
                        {
                            "$lookup": {
                                "from": "selection_type",
                                "localField": "selection_type_id.$id",
                                "foreignField": "_id",
                                "as": "options",
                            }
                        },
                        {
                            "$match": {
                                "options.name": PRIOR_ABLATION,
                                "options.deleted_at": None,
                            }
                        },
                    ],
                    "as": "prior_ablation_option",
                }
            },
            {
                "$lookup": {
                    "from": "laao_string_selections",
                    "localField": "rationale.$id",
                    "foreignField": "_id",
                    "as": "rationale_info",
                }
            },
            {
                "$unwind": {
                    "path": "$rationale_info",
                    "preserveNullAndEmptyArrays": True,
                }
            },
            {
                "$match": {
                    "rationale_info.deleted_at": None,
                }
            },
            {
                "$lookup": {
                    "from": "laao_string_selections",
                    "pipeline": [
                        {
                            "$match": {
                                "deleted_at": None,
                            }
                        },
                        {
                            "$lookup": {
                                "from": "selection_type",
                                "localField": "selection_type_id.$id",
                                "foreignField": "_id",
                                "as": "options",
                            }
                        },
                        {
                            "$match": {
                                "options.name": RATIONALE,
                                "options.deleted_at": None,
                            }
                        },
                    ],
                    "as": "rationale_option",
                }
            },
            {
                "$lookup": {
                    "from": "laao_string_selections",
                    "localField": "secondary_rationale.$id",
                    "foreignField": "_id",
                    "as": "secondary_rationale_info",
                }
            },
            {
                "$unwind": {
                    "path": "$secondary_rationale_info",
                    "preserveNullAndEmptyArrays": True,
                }
            },
            {
                "$match": {
                    "secondary_rationale_info.deleted_at": None,
                }
            },
            {
                "$lookup": {
                    "from": "laao_string_selections",
                    "localField": "anticoagulant.dosing_frequency.$id",
                    "foreignField": "_id",
                    "as": "dosing_frequency_val",
                }
            },
            {
                "$unwind": {
                    "path": "$dosing_frequency_val",
                    "preserveNullAndEmptyArrays": True,
                }
            },
            {
                "$match": {
                    "dosing_frequency_val.deleted_at": None,
                }
            },
            {
                "$group": {
                    "_id": "$_id",
                    "implanting_physician_id": {"$first": "$implanting_physician_id"},
                    "site_id": {"$first": "$site_id"},
                    "rep_id": {"$first": "$rep_id"},
                    "procedure_type_id": {"$first": "$procedure_type_id"},
                    "procedure_date": {"$first": "$procedure_date"},
                    "procedure_time": {"$first": "$procedure_time"},
                    "patient_id": {"$first": "$patient_id"},
                    "rationale": {"$first": "$rationale_info"},
                    "rationale_other": {"$first": "$rationale_other"},
                    "rationale_option": {"$first": "$rationale_option"},
                    "cta": {"$first": "$cta"},
                    "tee": {"$first": "$tee"},
                    "afib_ablation": {"$first": "$afib_ablation"},
                    "prior_ablation": {"$first": "$prior_ablation_info"},
                    "prior_ablation_other": {"$first": "$prior_ablation_other"},
                    "prior_ablation_option": {"$first": "$prior_ablation_option"},
                    "truplan_pdf_filename": {"$first": "$truplan_pdf_filename"},
                    "secondary_rationale": {"$first": "$secondary_rationale_info"},
                    "secondary_rationale_other": {
                        "$first": "$secondary_rationale_other"
                    },
                    "secondary_rationale_option": {
                        "$first": "$secondary_rationale_option"
                    },
                    "complication": {"$first": "$complication"},
                    "afib_classification_type_id": {
                        "$first": "$afib_classification_type_id"
                    },
                    "dosing_frequency_val": {"$first": "$dosing_frequency_val"},
                    "anticoagulation_val": {
                        "$push": {
                            "id": {"$ifNull": ["$medicine_details._id", None]},
                            "name": {"$ifNull": ["$medicine_details.name", None]},
                            "quantity": {"$ifNull": ["$anticoagulant.quantity", None]},
                            "dosing_frequency": {
                                "id": {"$ifNull": ["$dosing_frequency_val._id", None]},
                                "name": {
                                    "$ifNull": ["$dosing_frequency_val.name", None]
                                },
                            },
                        }
                    },
                    "truplan_upload_status": {"$first": "$truplan_upload_status"},
                }
            },
            {
                "$set": {
                    "anticoagulation_val": {
                        "$filter": {
                            "input": "$anticoagulation_val",
                            "as": "item",
                            "cond": {
                                "$and": [
                                    {"$ne": ["$$item.dosing_frequency", {}]},
                                    {"$ne": ["$$item", {}]},
                                ]
                            },
                        }
                    }
                }
            },
            {
                "$lookup": {
                    "from": "laao_string_selections",
                    "pipeline": [
                        {
                            "$match": {
                                "deleted_at": None,
                            }
                        },
                        {
                            "$lookup": {
                                "from": "selection_type",
                                "localField": "selection_type_id.$id",
                                "foreignField": "_id",
                                "as": "options",
                            }
                        },
                        {
                            "$match": {
                                "options.name": DOSING_FREQUENCY,
                                "options.deleted_at": None,
                            }
                        },
                    ],
                    "as": "dosing_frequency_option",
                }
            },
            # {
            #     "$lookup": {
            #         "from": "medicines",
            #         "localField": "anticoagulant.medicine.$id",
            #         "foreignField": "_id",
            #         "as": "anticoagulant_info",
            #     }
            # },
            {
                "$lookup": {
                    "from": "medicines",
                    "pipeline": [
                        {
                            "$match": {
                                "deleted_at": None,
                            }
                        },
                        {
                            "$lookup": {
                                "from": "medicine_type",
                                "localField": "drug_type.$id",
                                "foreignField": "_id",
                                "as": "anticoagulation_opt_selection",
                            }
                        },
                        {
                            "$match": {
                                "anticoagulation_opt_selection.name": ANTICOAGULANT,
                                "anticoagulation_opt_selection.deleted_at": None,
                            }
                        },
                        {
                            "$lookup": {
                                "from": "laao_string_selections",
                                "localField": "quantity.$id",
                                "foreignField": "_id",
                                "as": "quantity_opt",
                            }
                        },
                        {
                            "$addFields": {
                                "custom_medicines_order": {
                                    "$cond": {
                                        "if": {
                                            "$in": [
                                                "$name",
                                                CUSTOM_MEDICINES_ORDER,
                                            ]
                                        },
                                        "then": {
                                            "$indexOfArray": [
                                                CUSTOM_MEDICINES_ORDER,
                                                "$name",
                                            ]
                                        },
                                        "else": 9999,
                                    }
                                }
                            }
                        },
                        {"$sort": {"custom_medicines_order": 1}},
                    ],
                    "as": "anticoagulant_options",
                }
            },
            {
                "$match": {
                    "anticoagulant_options.deleted_at": None,
                }
            },
            {
                "$lookup": {
                    "from": "patient",
                    "localField": "patient_id.$id",
                    "foreignField": "_id",
                    "as": "patient_info",
                }
            },
            {"$unwind": {"path": "$patient_info", "preserveNullAndEmptyArrays": True}},
            {
                "$lookup": {
                    "from": "referring_provider",
                    "localField": "patient_info.pcp.$id",
                    "foreignField": "_id",
                    "as": "pcp_info",
                }
            },
            {
                "$lookup": {
                    "from": "referring_provider",
                    "pipeline": [
                        {
                            "$match": {
                                "deleted_at": None,
                                "specialty": "pcp",
                            }
                        },
                        {
                            "$lookup": {
                                "from": "referring_provider_credentials",
                                "localField": "credential.$id",
                                "foreignField": "_id",
                                "as": "credential_info",
                            }
                        },
                        {
                            "$addFields": {
                                "credential": {
                                    "$arrayElemAt": ["$credential_info.name", 0]
                                }
                            }
                        },
                    ],
                    "as": "pcp_options",
                }
            },
            {
                "$lookup": {
                    "from": "referring_provider",
                    "localField": "patient_info.referring_providers.$id",
                    "foreignField": "_id",
                    "as": "ref_provider",
                }
            },
            {
                "$lookup": {
                    "from": "referring_provider",
                    "pipeline": [
                        {
                            "$match": {
                                "deleted_at": None,
                                "$or": [
                                    {"specialty": {"$ne": "pcp"}},
                                    {"specialty": {"$exists": False}},
                                ],
                            }
                        },
                        {
                            "$lookup": {
                                "from": "referring_provider_credentials",
                                "localField": "credential.$id",
                                "foreignField": "_id",
                                "as": "credential_info",
                            }
                        },
                        {
                            "$addFields": {
                                "credential": {
                                    "$arrayElemAt": ["$credential_info.name", 0]
                                }
                            }
                        },
                    ],
                    "as": "referring_provider_options",
                }
            },
            {
                "$lookup": {
                    "from": "implanting_physicians",
                    "localField": "implanting_physician_id.$id",
                    "foreignField": "_id",
                    "as": "implanting_physician",
                }
            },
            {
                "$unwind": {
                    "path": "$implanting_physician",
                    "preserveNullAndEmptyArrays": True,
                }
            },
            {
                "$lookup": {
                    "from": "site",
                    "localField": "site_id.$id",
                    "foreignField": "_id",
                    "as": "site_info",
                }
            },
            {"$unwind": {"path": "$site_info", "preserveNullAndEmptyArrays": True}},
            {
                "$lookup": {
                    "from": "implanting_physicians",
                    "localField": "site_id.$id",
                    "foreignField": "sites.$id",
                    "as": "implant_opt",
                }
            },
        ]
        if (
            user_db_data.get("role") == REPROLE
            or user_db_data.get("role") == REP_ADVISOR
        ):
            pipeline.append(
                {
                    "$lookup": {
                        "from": "site",
                        "pipeline": [
                            {
                                "$match": {
                                    "deleted_at": None,
                                }
                            },
                        ],
                        "as": "site_options",  ############################################################
                    }
                },
            )
            pipeline.append(
                {
                    "$project": {
                        "_id": 0,
                        "case_id": "$_id",
                        "procedure_date": "$procedure_date",
                        "procedure_time": "$procedure_time",
                        "patient": {
                            "id": "$patient_info._id",
                            "name": {
                                "$concat": [
                                    "$patient_info.first_name",
                                    " ",
                                    "$patient_info.last_name",
                                ]
                            },
                            "first_name": "$patient_info.first_name",
                            "last_name": "$patient_info.last_name",
                            "middle_name": {
                                "$ifNull": [
                                    "$patient_info.middle_name",
                                    "",
                                ]
                            },
                            "age": {
                                "$subtract": [
                                    {
                                        "$year": datetime.now(timezone.utc)
                                    },  # Current year
                                    {
                                        "$year": {
                                            "$dateFromString": {
                                                "dateString": "$patient_info.dob"
                                            }
                                        }
                                    },  # Year of DOB
                                ]
                            },
                            "sex": "$patient_info.sex",
                            "cta": "$cta",
                            "tee": "$tee",
                            "afib_ablation": "$afib_ablation",
                            "rationale": {
                                "selected": {
                                    "id": "$rationale._id",
                                    "name": "$rationale.name",
                                    "other": "$rationale_other",
                                },
                                "options": {
                                    "$map": {
                                        "input": "$rationale_option",
                                        "as": "option",
                                        "in": {
                                            "id": "$$option._id",
                                            "name": "$$option.name",
                                        },
                                    }
                                },
                            },
                            "dob": "$patient_info.dob",
                            "prior_ablation": {
                                "selected": {
                                    "id": "$prior_ablation._id",
                                    "name": "$prior_ablation.name",
                                    "other": "$prior_ablation_other",
                                },
                                "options": {
                                    "$map": {
                                        "input": "$prior_ablation_option",
                                        "as": "option",
                                        "in": {
                                            "id": "$$option._id",
                                            "name": "$$option.name",
                                        },
                                    }
                                },
                            },
                            "pcp": {
                                "selected": {
                                    "id": {"$first": "$pcp_info._id"},
                                    "name": {
                                        "$concat": [
                                            {"$first": "$pcp_info.first_name"},
                                            " ",
                                            {"$first": "$pcp_info.last_name"},
                                            ", ",
                                            {"$first": "$credential_info.name"},
                                        ]
                                    },
                                },
                                "options": {
                                    "$map": {
                                        "input": "$pcp_options",
                                        "as": "option",
                                        "in": {
                                            "id": "$$option._id",
                                            "name": {
                                                "$concat": [
                                                    "$$option.first_name",
                                                    " ",
                                                    "$$option.last_name",
                                                    ", ",
                                                    {
                                                        "$ifNull": [
                                                            "$$option.credential",
                                                            "",
                                                        ]
                                                    },
                                                ]
                                            },
                                        },
                                    }
                                },
                            },
                            "referring_provider": {
                                "selected": {
                                    "id": {"$first": "$ref_provider._id"},
                                    "name": {
                                        "$concat": [
                                            {"$first": "$ref_provider.first_name"},
                                            " ",
                                            {"$first": "$ref_provider.last_name"},
                                            ", ",
                                            {"$first": "$credential_info.name"},
                                        ]
                                    },
                                },
                                "options": {
                                    "$map": {
                                        "input": "$referring_provider_options",
                                        "as": "option",
                                        "in": {
                                            "id": "$$option._id",
                                            "name": {
                                                "$concat": [
                                                    "$$option.first_name",
                                                    " ",
                                                    "$$option.last_name",
                                                    ", ",
                                                    "$$option.credential",
                                                ]
                                            },
                                        },
                                    }
                                },
                            },
                            "anticoagulation": {
                                "selected": "$anticoagulation_val",
                                "options": {
                                    "$map": {
                                        "input": "$anticoagulant_options",
                                        "as": "option",
                                        "in": {
                                            "id": "$$option._id",
                                            "name": "$$option.name",
                                            "quantity": {
                                                "$map": {
                                                    "input": "$$option.quantity_opt",
                                                    "as": "opt",
                                                    "in": "$$opt.name",
                                                }
                                            },
                                        },
                                    }
                                },
                                "frequency_options": {
                                    "$map": {
                                        "input": "$dosing_frequency_option",
                                        "as": "option",
                                        "in": {
                                            "id": "$$option._id",
                                            "name": "$$option.name",
                                        },
                                    }
                                },
                            },
                            "truplan_upload_status": "$truplan_upload_status",
                        },
                        "implanting_physician": {
                            "selected": {
                                "id": "$implanting_physician._id",
                                "name": {
                                    "$concat": [
                                        "$implanting_physician.first_name",
                                        " ",
                                        "$implanting_physician.last_name",
                                        ", ",
                                        {
                                            "$ifNull": [
                                                "$implanting_physician.credential",
                                                "",
                                            ]
                                        },
                                    ]
                                },
                            },
                            "options": {
                                "$map": {
                                    "input": "$implant_opt",
                                    "as": "option",
                                    "in": {
                                        "id": "$$option._id",
                                        "name": {
                                            "$concat": [
                                                "$$option.first_name",
                                                " ",
                                                "$$option.last_name",
                                                ", ",
                                                {
                                                    "$ifNull": [
                                                        "$$option.credential",
                                                        "",
                                                    ]
                                                },
                                            ]
                                        },
                                    },
                                }
                            },
                        },
                        "site": {
                            "selected": {
                                "id": "$site_info._id",
                                "name": "$site_info.name",
                                "image_url": "$site_info.image_url",
                            },
                            "options": {
                                "$map": {
                                    "input": "$site_options",
                                    "as": "option",
                                    "in": {
                                        "id": "$$option._id",
                                        "name": "$$option.name",
                                    },
                                }
                            },
                        },
                    }
                }
            )
        elif user_db_data.get("role") == COORDROLE:
            coord_pipeline = [
                {
                    "$lookup": {
                        "from": "consult_visit",
                        "let": {"case_id": "$_id"},
                        "pipeline": [
                            {
                                "$match": {
                                    "$expr": {"$eq": ["$case_id.$id", "$$case_id"]},
                                    "deleted_at": None,
                                }
                            },
                            {
                                "$lookup": {
                                    "from": "implanting_physicians",
                                    "localField": "physician_id.$id",
                                    "foreignField": "_id",
                                    "as": "consult_visit_implanting_physician",
                                }
                            },
                            {
                                "$match": {
                                    "consult_visit_implanting_physician.deleted_at": None
                                }
                            },
                            {
                                "$project": {
                                    "_id": 0,
                                    "visit_date": 1,
                                    "visit_type": 1,
                                    "updated_at": 1,
                                    "physician_name": {
                                        "$concat": [
                                            {
                                                "$arrayElemAt": [
                                                    "$consult_visit_implanting_physician.first_name",
                                                    0,
                                                ]
                                            },
                                            " ",
                                            {
                                                "$arrayElemAt": [
                                                    "$consult_visit_implanting_physician.last_name",
                                                    0,
                                                ]
                                            },
                                            ", ",
                                            {
                                                "$arrayElemAt": [
                                                    "$consult_visit_implanting_physician.credential",
                                                    0,
                                                ]
                                            },
                                        ]
                                    },
                                }
                            },
                            # {"$sort": {"updated_at": -1}},
                        ],
                        "as": "consult_visit_details",
                    }
                },
                {
                    "$lookup": {
                        "from": "lab_details",
                        "let": {"case_id": "$_id"},
                        "pipeline": [
                            {
                                "$match": {
                                    "$expr": {"$eq": ["$case_id.$id", "$$case_id"]},
                                    "deleted_at": None,
                                }
                            },
                            {"$sort": {"updated_at": -1}},
                            {
                                "$group": {
                                    "_id": "$patient_id",
                                    "latest_lab_detail": {"$first": "$$ROOT"},
                                }
                            },
                            {"$replaceRoot": {"newRoot": "$latest_lab_detail"}},
                            {
                                "$lookup": {
                                    "from": "afib_string_selections",
                                    "localField": "rhythm_id.$id",
                                    "foreignField": "_id",
                                    "as": "rhythm_info",
                                }
                            },
                            {
                                "$unwind": {
                                    "path": "$rhythm_info",
                                    "preserveNullAndEmptyArrays": True,
                                }
                            },
                            {"$match": {"rhythm_info.deleted_at": None}},
                            {
                                "$project": {
                                    "_id": 0,
                                    "hemoglobin": 1,
                                    "hemoglobin_updated_at": {
                                        "$dateToString": {
                                            "format": "%Y-%m-%d",
                                            "date": {
                                                "$toDate": "$hemoglobin_updated_at"
                                            },
                                        }
                                    },
                                    "platelets": 1,
                                    "platelets_updated_at": {
                                        "$dateToString": {
                                            "format": "%Y-%m-%d",
                                            "date": {
                                                "$toDate": "$platelets_updated_at"
                                            },
                                        }
                                    },
                                    "creatinine": 1,
                                    "creatinine_updated_at": {
                                        "$dateToString": {
                                            "format": "%Y-%m-%d",
                                            "date": {
                                                "$toDate": "$creatinine_updated_at"
                                            },
                                        }
                                    },
                                    "egfr": 1,
                                    "egfr_updated_at": {
                                        "$dateToString": {
                                            "format": "%Y-%m-%d",
                                            "date": {"$toDate": "$egfr_updated_at"},
                                        }
                                    },
                                    "rhythm_id": {
                                        "selected": {
                                            "id": "$rhythm_info._id",
                                            "name": "$rhythm_info.name",
                                        },
                                    },
                                    "ventricular_rate": 1,
                                    "updated_at": {
                                        "$dateToString": {
                                            "format": "%Y-%m-%d",
                                            "date": {"$toDate": "$updated_at"},
                                        }
                                    },
                                }
                            },
                        ],
                        "as": "lab_details",
                    }
                },
                {
                    "$lookup": {
                        "from": "afib_string_selections",
                        "pipeline": [
                            {
                                "$lookup": {
                                    "from": "afib_selection_type",
                                    "localField": "selection_type_id.$id",
                                    "foreignField": "_id",
                                    "as": "rhythm_option",
                                }
                            },
                            {"$match": {"rhythm_option.name": LAB_EKG_RHYTHM}},
                        ],
                        "as": "rhythm_option",
                    }
                },
                {
                    "$lookup": {
                        "from": "afib_string_selections",
                        "localField": "afib_classification_type_id.$id",
                        "foreignField": "_id",
                        "as": "afib_classification_info",
                    }
                },
                {
                    "$unwind": {
                        "path": "$afib_classification_info",
                        "preserveNullAndEmptyArrays": True,
                    }
                },
                {"$match": {"afib_classification_info.deleted_at": None}},
                {
                    "$lookup": {
                        "from": "afib_string_selections",
                        "pipeline": [
                            {
                                "$lookup": {
                                    "from": "afib_selection_type",
                                    "localField": "selection_type_id.$id",
                                    "foreignField": "_id",
                                    "as": "afib_classification_option",
                                }
                            },
                            {
                                "$match": {
                                    "afib_classification_option.name": AFIB_CLASSIFICATION
                                }
                            },
                        ],
                        "as": "afib_classification_option",
                    }
                },
                {
                    "$project": {
                        "_id": 0,
                        "case_id": "$_id",
                        "procedure_date": "$procedure_date",
                        "procedure_time": "$procedure_time",
                        "patient": {
                            "id": "$patient_info._id",
                            "first_name": "$patient_info.first_name",
                            "last_name": "$patient_info.last_name",
                            "middle_name": {
                                "$ifNull": [
                                    "$patient_info.middle_name",
                                    "",
                                ]
                            },
                            "age": {
                                "$subtract": [
                                    {
                                        "$year": datetime.now(timezone.utc)
                                    },  # Current year
                                    {
                                        "$year": {
                                            "$dateFromString": {
                                                "dateString": "$patient_info.dob"
                                            }
                                        }
                                    },  # Year of DOB
                                ]
                            },
                            "sex": "$patient_info.sex",
                            "cta": "$cta",
                            "tee": "$tee",
                            "afib_ablation": "$afib_ablation",
                            "rationale": {
                                "primary": {
                                    "selected": {
                                        "id": "$rationale._id",
                                        "name": "$rationale.name",
                                        "other": "$rationale_other",
                                    },
                                    "options": {
                                        "$map": {
                                            "input": "$rationale_option",
                                            "as": "option",
                                            "in": {
                                                "id": "$$option._id",
                                                "name": "$$option.name",
                                            },
                                        }
                                    },
                                },
                                "secondary": {
                                    "selected": {
                                        "id": "$secondary_rationale._id",
                                        "name": "$secondary_rationale.name",
                                        "other": "$secondary_rationale_other",
                                    },
                                    "options": {
                                        "$map": {
                                            "input": "$rationale_option",
                                            "as": "option",
                                            "in": {
                                                "id": "$$option._id",
                                                "name": "$$option.name",
                                            },
                                        }
                                    },
                                },
                            },
                            "social_history": {
                                "home_address": {
                                    "address": "$patient_info.address",
                                    "city": "$patient_info.city",
                                    "state": "$patient_info.state",
                                },
                                "distance": "$patient_info.distance",
                                "lives_independent": "$patient_info.lives_independent",
                                "has_social_support": "$patient_info.has_social_support",
                            },
                            "dob": "$patient_info.dob",
                            "prior_ablation": {
                                "selected": {
                                    "id": "$prior_ablation._id",
                                    "name": "$prior_ablation.name",
                                    "other": "$prior_ablation_other",
                                },
                                "options": {
                                    "$map": {
                                        "input": "$prior_ablation_option",
                                        "as": "option",
                                        "in": {
                                            "id": "$$option._id",
                                            "name": "$$option.name",
                                        },
                                    }
                                },
                            },
                            "afib_classification": {
                                "selected": {
                                    "id": "$afib_classification_info._id",
                                    "name": "$afib_classification_info.name",
                                },
                                "options": {
                                    "$let": {
                                        "vars": {
                                            "sorted_options": {
                                                "$cond": {
                                                    "if": {
                                                        "$isArray": "$afib_classification_option"
                                                    },
                                                    "then": {
                                                        "$sortArray": {
                                                            "input": {
                                                                "$map": {
                                                                    "input": "$afib_classification_option",
                                                                    "as": "option",
                                                                    "in": {
                                                                        "id": "$$option._id",
                                                                        "name": "$$option.name",
                                                                        "order": "$$option.order",
                                                                    },
                                                                }
                                                            },
                                                            "sortBy": {"order": 1},
                                                        }
                                                    },
                                                    "else": [],
                                                }
                                            }
                                        },
                                        "in": "$$sorted_options",
                                    }
                                },
                            },
                            "pcp": {
                                "selected": {
                                    "id": {"$first": "$pcp_info._id"},
                                    "name": {
                                        "$concat": [
                                            {"$first": "$pcp_info.first_name"},
                                            " ",
                                            {"$first": "$pcp_info.last_name"},
                                            ", ",
                                            {"$first": "$credential_info.name"},
                                        ]
                                    },
                                },
                                "options": {
                                    "$map": {
                                        "input": "$pcp_options",
                                        "as": "option",
                                        "in": {
                                            "id": "$$option._id",
                                            "name": {
                                                "$concat": [
                                                    "$$option.first_name",
                                                    " ",
                                                    "$$option.last_name",
                                                    ", ",
                                                    {
                                                        "$ifNull": [
                                                            "$$option.credential",
                                                            "",
                                                        ]
                                                    },
                                                ]
                                            },
                                        },
                                    }
                                },
                            },
                            "referring_provider": {
                                "selected": {
                                    "id": {"$first": "$ref_provider._id"},
                                    "name": {
                                        "$concat": [
                                            {"$first": "$ref_provider.first_name"},
                                            " ",
                                            {"$first": "$ref_provider.last_name"},
                                            ", ",
                                            {"$first": "$credential_info.name"},
                                        ]
                                    },
                                },
                                "options": {
                                    "$map": {
                                        "input": "$referring_provider_options",
                                        "as": "option",
                                        "in": {
                                            "id": "$$option._id",
                                            "name": {
                                                "$concat": [
                                                    "$$option.first_name",
                                                    " ",
                                                    "$$option.last_name",
                                                    ", ",
                                                    "$$option.credential",
                                                ]
                                            },
                                        },
                                    }
                                },
                            },
                            "anticoagulation": {
                                "selected": "$anticoagulation_val",
                                "options": {
                                    "$map": {
                                        "input": "$anticoagulant_options",
                                        "as": "option",
                                        "in": {
                                            "id": "$$option._id",
                                            "name": "$$option.name",
                                            "quantity": {
                                                "$map": {
                                                    "input": "$$option.quantity_opt",
                                                    "as": "opt",
                                                    "in": "$$opt.name",
                                                }
                                            },
                                        },
                                    }
                                },
                                "frequency_options": {
                                    "$map": {
                                        "input": "$dosing_frequency_option",
                                        "as": "option",
                                        "in": {
                                            "id": "$$option._id",
                                            "name": "$$option.name",
                                        },
                                    }
                                },
                            },
                            "truplan_upload_status": "$truplan_upload_status",
                        },
                        "implanting_physician": {
                            "selected": {
                                "id": "$implanting_physician._id",
                                "name": {
                                    "$concat": [
                                        "$implanting_physician.first_name",
                                        " ",
                                        "$implanting_physician.last_name",
                                        ", ",
                                        {
                                            "$ifNull": [
                                                "$implanting_physician.credential",
                                                "",
                                            ]
                                        },
                                    ]
                                },
                            },
                            "options": {
                                "$map": {
                                    "input": "$implant_opt",
                                    "as": "option",
                                    "in": {
                                        "id": "$$option._id",
                                        "name": {
                                            "$concat": [
                                                "$$option.first_name",
                                                " ",
                                                "$$option.last_name",
                                                ", ",
                                                {
                                                    "$ifNull": [
                                                        "$$option.credential",
                                                        "",
                                                    ]
                                                },
                                            ]
                                        },
                                    },
                                }
                            },
                        },
                        "site": {
                            "id": "$site_info._id",
                            "name": "$site_info.name",
                            "image_url": "$site_info.image_url",
                            "state": {
                                "$concat": [
                                    "$site_info.city",
                                    ", ",
                                    "$site_info.state",
                                ]
                            },
                        },
                        "consult_visit": {
                            "pre_op": {
                                "$filter": {
                                    "input": "$consult_visit_details",
                                    "as": "visit",
                                    "cond": {
                                        "$and": [
                                            {"$eq": ["$$visit.visit_type", "pre-op"]},
                                            {
                                                "$lt": [
                                                    {"$toDate": "$$visit.visit_date"},
                                                    current_datetime,
                                                ]
                                            },
                                        ]
                                    },
                                }
                            },
                            "post_op": {
                                "$filter": {
                                    "input": "$consult_visit_details",
                                    "as": "visit",
                                    "cond": {
                                        "$and": [
                                            {"$eq": ["$$visit.visit_type", "post-op"]},
                                            {
                                                "$lt": [
                                                    {"$toDate": "$$visit.visit_date"},
                                                    current_datetime,
                                                ]
                                            },
                                        ],
                                        # "$eq": ["$$visit.visit_type", "post-op"]
                                    },
                                }
                            },
                            "scheduled": {
                                "$filter": {
                                    "input": "$consult_visit_details",
                                    "as": "visit",
                                    "cond": {
                                        "$gte": [
                                            {"$toDate": "$$visit.visit_date"},
                                            current_datetime,
                                        ]
                                    },
                                }
                            },
                        },
                        "lab_details": {
                            "$cond": {
                                "if": {"$eq": [{"$size": "$lab_details"}, 0]},
                                "then": {
                                    "hemoglobin": None,
                                    "hemoglobin_updated_at": None,
                                    "platelets": None,
                                    "platelets_updated_at": None,
                                    "creatinine": None,
                                    "creatinine_updated_at": None,
                                    "egfr": None,
                                    "egfr_updated_at": None,
                                    "ventricular_rate": None,
                                    "updated_at": None,
                                    "rhythm_details": {
                                        "selected": {},
                                        "options": {
                                            "$map": {
                                                "input": "$rhythm_option",
                                                "as": "option",
                                                "in": {
                                                    "id": "$$option._id",
                                                    "name": "$$option.name",
                                                },
                                            }
                                        },
                                    },
                                },
                                "else": {
                                    "hemoglobin": {
                                        "$ifNull": [
                                            {
                                                "$arrayElemAt": [
                                                    "$lab_details.hemoglobin",
                                                    0,
                                                ]
                                            },
                                            None,
                                        ]
                                    },
                                    "hemoglobin_updated_at": {
                                        "$ifNull": [
                                            {
                                                "$arrayElemAt": [
                                                    "$lab_details.hemoglobin_updated_at",
                                                    0,
                                                ]
                                            },
                                            None,
                                        ]
                                    },
                                    "platelets": {
                                        "$ifNull": [
                                            {
                                                "$arrayElemAt": [
                                                    "$lab_details.platelets",
                                                    0,
                                                ]
                                            },
                                            None,
                                        ]
                                    },
                                    "platelets_updated_at": {
                                        "$ifNull": [
                                            {
                                                "$arrayElemAt": [
                                                    "$lab_details.platelets_updated_at",
                                                    0,
                                                ]
                                            },
                                            None,
                                        ]
                                    },
                                    "creatinine": {
                                        "$ifNull": [
                                            {
                                                "$arrayElemAt": [
                                                    "$lab_details.creatinine",
                                                    0,
                                                ]
                                            },
                                            None,
                                        ]
                                    },
                                    "creatinine_updated_at": {
                                        "$ifNull": [
                                            {
                                                "$arrayElemAt": [
                                                    "$lab_details.creatinine_updated_at",
                                                    0,
                                                ]
                                            },
                                            None,
                                        ]
                                    },
                                    "egfr": {
                                        "$ifNull": [
                                            {"$arrayElemAt": ["$lab_details.egfr", 0]},
                                            None,
                                        ]
                                    },
                                    "egfr_updated_at": {
                                        "$ifNull": [
                                            {
                                                "$arrayElemAt": [
                                                    "$lab_details.egfr_updated_at",
                                                    0,
                                                ]
                                            },
                                            None,
                                        ]
                                    },
                                    "ventricular_rate": {
                                        "$ifNull": [
                                            {
                                                "$arrayElemAt": [
                                                    "$lab_details.ventricular_rate",
                                                    0,
                                                ]
                                            },
                                            None,
                                        ]
                                    },
                                    "updated_at": {
                                        "$ifNull": [
                                            {
                                                "$arrayElemAt": [
                                                    "$lab_details.updated_at",
                                                    0,
                                                ]
                                            },
                                            None,
                                        ]
                                    },
                                    "rhythm_details": {
                                        "selected": {
                                            "$ifNull": [
                                                {
                                                    "$arrayElemAt": [
                                                        "$lab_details.rhythm_id.selected",
                                                        0,
                                                    ]
                                                },
                                                {},
                                            ]
                                        },
                                        "options": {
                                            "$map": {
                                                "input": "$rhythm_option",
                                                "as": "option",
                                                "in": {
                                                    "id": "$$option._id",
                                                    "name": "$$option.name",
                                                },
                                            }
                                        },
                                    },
                                },
                            }
                        },
                    }
                },
            ]

            pipeline.extend(coord_pipeline)
        elif user_db_data.get("role") == CLINICIAN_ROLE:
            coord_pipeline = [
                {
                    "$lookup": {
                        "from": "afib_string_selections",
                        "pipeline": [
                            {
                                "$lookup": {
                                    "from": "afib_selection_type",
                                    "localField": "selection_type_id.$id",
                                    "foreignField": "_id",
                                    "as": "rhythm_option",
                                }
                            },
                            {"$match": {"rhythm_option.name": LAB_EKG_RHYTHM}},
                        ],
                        "as": "rhythm_option",
                    }
                },
                {
                    "$lookup": {
                        "from": "afib_string_selections",
                        "localField": "afib_classification_type_id.$id",
                        "foreignField": "_id",
                        "as": "afib_classification_info",
                    }
                },
                {
                    "$unwind": {
                        "path": "$afib_classification_info",
                        "preserveNullAndEmptyArrays": True,
                    }
                },
                {"$match": {"afib_classification_info.deleted_at": None}},
                {
                    "$lookup": {
                        "from": "laao_procedure_details",
                        "localField": "_id",
                        "foreignField": "case_id.$id",
                        "as": "laao_procedure_details_info",
                    }
                },
                {
                    "$unwind": {
                        "path": "$laao_procedure_details_info",
                        "preserveNullAndEmptyArrays": True,
                    }
                },
                {"$match": {"laao_procedure_details_info.deleted_at": None}},
                {
                    "$lookup": {
                        "from": "afib_string_selections",
                        "pipeline": [
                            {
                                "$lookup": {
                                    "from": "afib_selection_type",
                                    "localField": "selection_type_id.$id",
                                    "foreignField": "_id",
                                    "as": "afib_classification_option",
                                }
                            },
                            {
                                "$match": {
                                    "afib_classification_option.name": AFIB_CLASSIFICATION
                                }
                            },
                        ],
                        "as": "afib_classification_option",
                    }
                },
                {
                    "$project": {
                        "_id": 0,
                        "case_id": "$_id",
                        "procedure_date": "$procedure_date",
                        "procedure_time": "$procedure_time",
                        "patient": {
                            "id": "$patient_info._id",
                            "first_name": "$patient_info.first_name",
                            "last_name": "$patient_info.last_name",
                            "middle_name": {
                                "$ifNull": [
                                    "$patient_info.middle_name",
                                    "",
                                ]
                            },
                            "age": {
                                "$subtract": [
                                    {
                                        "$year": datetime.now(timezone.utc)
                                    },  # Current year
                                    {
                                        "$year": {
                                            "$dateFromString": {
                                                "dateString": "$patient_info.dob"
                                            }
                                        }
                                    },  # Year of DOB
                                ]
                            },
                            "bmi": "$patient_info.bmi",
                            "major_comorbidities": "$laao_procedure_details_info.major_comorbidities",
                            "prior_bleeding_events": "$laao_procedure_details_info.prior_bleeding_events",
                            "dementia_presence": "$laao_procedure_details_info.dementia_presence",
                            "sex": "$patient_info.sex",
                            "cta": "$cta",
                            "tee": "$tee",
                            "afib_ablation": "$afib_ablation",
                            "rationale": {
                                "primary": {
                                    "selected": {
                                        "id": "$rationale._id",
                                        "name": "$rationale.name",
                                        "other": "$rationale_other",
                                    },
                                    "options": {
                                        "$map": {
                                            "input": "$rationale_option",
                                            "as": "option",
                                            "in": {
                                                "id": "$$option._id",
                                                "name": "$$option.name",
                                            },
                                        }
                                    },
                                },
                                "secondary": {
                                    "selected": {
                                        "id": "$secondary_rationale._id",
                                        "name": "$secondary_rationale.name",
                                        "other": "$secondary_rationale_other",
                                    },
                                    "options": {
                                        "$map": {
                                            "input": "$rationale_option",
                                            "as": "option",
                                            "in": {
                                                "id": "$$option._id",
                                                "name": "$$option.name",
                                            },
                                        }
                                    },
                                },
                            },
                            "social_history": {
                                "home_address": {
                                    "address": "$patient_info.address",
                                    "city": "$patient_info.city",
                                    "state": "$patient_info.state",
                                },
                                "distance": "$patient_info.distance",
                                "lives_independent": "$patient_info.lives_independent",
                                "has_social_support": "$patient_info.has_social_support",
                            },
                            "dob": "$patient_info.dob",
                            "prior_ablation": {
                                "selected": {
                                    "id": "$prior_ablation._id",
                                    "name": "$prior_ablation.name",
                                    "other": "$prior_ablation_other",
                                },
                                "options": {
                                    "$map": {
                                        "input": "$prior_ablation_option",
                                        "as": "option",
                                        "in": {
                                            "id": "$$option._id",
                                            "name": "$$option.name",
                                        },
                                    }
                                },
                            },
                            "afib_classification": {
                                "selected": {
                                    "id": "$afib_classification_info._id",
                                    "name": "$afib_classification_info.name",
                                },
                                "options": {
                                    "$let": {
                                        "vars": {
                                            "sorted_options": {
                                                "$cond": {
                                                    "if": {
                                                        "$isArray": "$afib_classification_option"
                                                    },
                                                    "then": {
                                                        "$sortArray": {
                                                            "input": {
                                                                "$map": {
                                                                    "input": "$afib_classification_option",
                                                                    "as": "option",
                                                                    "in": {
                                                                        "id": "$$option._id",
                                                                        "name": "$$option.name",
                                                                        "order": "$$option.order",
                                                                    },
                                                                }
                                                            },
                                                            "sortBy": {"order": 1},
                                                        }
                                                    },
                                                    "else": [],
                                                }
                                            }
                                        },
                                        "in": "$$sorted_options",
                                    }
                                },
                            },
                            "pcp": {
                                "selected": {
                                    "id": {"$first": "$pcp_info._id"},
                                    "name": {
                                        "$concat": [
                                            {"$first": "$pcp_info.first_name"},
                                            " ",
                                            {"$first": "$pcp_info.last_name"},
                                            ", ",
                                            {"$first": "$credential_info.name"},
                                        ]
                                    },
                                },
                                "options": {
                                    "$map": {
                                        "input": "$pcp_options",
                                        "as": "option",
                                        "in": {
                                            "id": "$$option._id",
                                            "name": {
                                                "$concat": [
                                                    "$$option.first_name",
                                                    " ",
                                                    "$$option.last_name",
                                                    ", ",
                                                    {
                                                        "$ifNull": [
                                                            "$$option.credential",
                                                            "",
                                                        ]
                                                    },
                                                ]
                                            },
                                        },
                                    }
                                },
                            },
                            "referring_provider": {
                                "selected": {
                                    "id": {"$first": "$ref_provider._id"},
                                    "name": {
                                        "$concat": [
                                            {"$first": "$ref_provider.first_name"},
                                            " ",
                                            {"$first": "$ref_provider.last_name"},
                                            ", ",
                                            {"$first": "$credential_info.name"},
                                        ]
                                    },
                                },
                                "options": {
                                    "$map": {
                                        "input": "$referring_provider_options",
                                        "as": "option",
                                        "in": {
                                            "id": "$$option._id",
                                            "name": {
                                                "$concat": [
                                                    "$$option.first_name",
                                                    " ",
                                                    "$$option.last_name",
                                                    ", ",
                                                    "$$option.credential",
                                                ]
                                            },
                                        },
                                    }
                                },
                            },
                            "anticoagulation": {
                                "selected": "$anticoagulation_val",
                                "options": {
                                    "$map": {
                                        "input": "$anticoagulant_options",
                                        "as": "option",
                                        "in": {
                                            "id": "$$option._id",
                                            "name": "$$option.name",
                                            "quantity": {
                                                "$map": {
                                                    "input": "$$option.quantity_opt",
                                                    "as": "opt",
                                                    "in": "$$opt.name",
                                                }
                                            },
                                        },
                                    }
                                },
                                "frequency_options": {
                                    "$map": {
                                        "input": "$dosing_frequency_option",
                                        "as": "option",
                                        "in": {
                                            "id": "$$option._id",
                                            "name": "$$option.name",
                                        },
                                    }
                                },
                            },
                        },
                        "implanting_physician": {
                            "selected": {
                                "id": "$implanting_physician._id",
                                "name": {
                                    "$concat": [
                                        "$implanting_physician.first_name",
                                        " ",
                                        "$implanting_physician.last_name",
                                        ", ",
                                        {
                                            "$ifNull": [
                                                "$implanting_physician.credential",
                                                "",
                                            ]
                                        },
                                    ]
                                },
                            },
                            "options": {
                                "$map": {
                                    "input": "$implant_opt",
                                    "as": "option",
                                    "in": {
                                        "id": "$$option._id",
                                        "name": {
                                            "$concat": [
                                                "$$option.first_name",
                                                " ",
                                                "$$option.last_name",
                                                ", ",
                                                {
                                                    "$ifNull": [
                                                        "$$option.credential",
                                                        "",
                                                    ]
                                                },
                                            ]
                                        },
                                    },
                                }
                            },
                        },
                        "site": {
                            "id": "$site_info._id",
                            "name": "$site_info.name",
                            "image_url": "$site_info.image_url",
                            "state": {
                                "$concat": [
                                    "$site_info.city",
                                    ", ",
                                    "$site_info.state",
                                ]
                            },
                        },
                    }
                },
            ]
            pipeline.extend(coord_pipeline)
        else:
            raise HTTPException(
                status.HTTP_403_FORBIDDEN, "you don't have access to this route"
            )

        data = collection.aggregate(pipeline).to_list()
        response.status_code = status.HTTP_200_OK

        # Append CHA2DS2-VASc score for each case
        if data:
            case = data[0]
            patient_id = case.get("patient", {}).get("id")
            case_id = case.get("case_id")

            ambra_params = {
                "filter.patientid.equals": str(patient_id),
                "filter.modality.equals": "CT",
            }

            case.get("patient", {}).update(
                {
                    "cha2ds2_vasc": get_cha2ds2_vasc_score(case_id, mongo_client),
                    "has_bled_score": get_has_bled_risk_score(case_id, mongo_client),
                    "study": get_ambra_image_url(ambra_params, mongo_client),
                }
            )

            return api_response(case, "Patient record fetched", "success")

        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="case not found")
    except Exception:
        raise


@router.get(
    "/sites/{site_id}/cases/{case_date}/rep-unassigned",
    response_model=ResponseSchema[PatientUnassignedCaseList],
)
async def get_rep_unassigned_cases_by_site_and_case_date(
    request: Request,
    response: Response,
    site_id: str,
    case_date: str,
    token_data: dict = Depends(authorizer),
):
    try:
        mongo_client: Database = request.app.database
        collection = mongo_client.get_collection("case_summary")
        pipeline = [
            {
                "$match": {
                    "rep_id": None,
                    "deleted_at": None,
                    "procedure_date": case_date,
                    "procedure_time": {"$ne": None},
                    "site_id.$id": ObjectId(site_id),
                }
            },
            {
                "$lookup": {
                    "from": "patient",
                    "localField": "patient_id.$id",
                    "foreignField": "_id",
                    "as": "patient_info",
                }
            },
            {"$unwind": "$patient_info"},
            {"$match": {"patient_info.deleted_at": None}},
            {
                "$project": {
                    "_id": 0,
                    "case_id": "$_id",
                    "procedure_date": "$procedure_date",
                    "procedure_time": "$procedure_time",
                    "patient": {
                        "id": "$patient_info._id",
                        "name": {
                            "$concat": [
                                "$patient_info.first_name",
                                " ",
                                "$patient_info.last_name",
                            ]
                        },
                        "age": {
                            "$subtract": [
                                {"$year": datetime.now(timezone.utc)},  # Current year
                                {
                                    "$year": {
                                        "$dateFromString": {
                                            "dateString": "$patient_info.dob"
                                        }
                                    }
                                },  # Year of DOB
                            ]
                        },
                        "sex": "$patient_info.sex",
                        "image_url": "$patient_info.image_url",
                        "dob": "$patient_info.dob",
                    },
                }
            },
        ]
        data = collection.aggregate(pipeline).to_list()
        response.status_code = status.HTTP_200_OK
        if data:
            return api_response(data, "Case data retrieved", "success")

        return api_response([], "No cases found", "success")

    except Exception:
        raise


@router.put("/cases/{case_id}/patient")
async def update_preop_details_by_rep(
    request: Request,
    response: Response,
    body: PreOpRepUpdate,
    case_id: str,
    token_data: dict = Depends(authorizer),
):
    try:
        filtered_data = body.model_dump(exclude_unset=True)
        mongo_client: Database = request.app.database
        if filtered_data:
            patient_payload = filtered_data.pop("patient", None)
            patient_collection = mongo_client.get_collection("patient")
            case_collection = mongo_client.get_collection("case_summary")
            case_res = case_collection.find_one_and_update(
                {"_id": ObjectId(case_id)},
                {"$set": filtered_data},
                return_document=True,
                projection={"patient_id": "$patient_id.$id"},
            )
            if not case_res:
                raise HTTPException(status.HTTP_404_NOT_FOUND, "case data not found")

            if patient_payload:
                patient_details = get_patient_details_by_case_id(case_id, mongo_client)
                if not patient_details:
                    raise HTTPException(status.HTTP_404_NOT_FOUND, "patient not found")
                patient_id = str(patient_details["patient_id"])
                update_patient = patient_collection.update_one(
                    {"_id": ObjectId(patient_id)}, {"$set": patient_payload}
                )
                embedded_data_points_agent(case_id, token_data["token"])

                return api_response([], "Case record updated", "success")
            else:
                raise HTTPException(status.HTTP_404_NOT_FOUND, "case data not found")
        else:
            return api_response([], "Nothing to update", "success")
    except Exception:
        raise
