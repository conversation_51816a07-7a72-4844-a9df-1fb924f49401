from lxml import etree
import xml.etree.ElementTree as ET
import base64
import os
import shutil
import pyzipper
from Cryptodome.Cipher import AES
import hashlib
import datetime
from v2.constants import FORM_TABLE_IDS, FORM_EVENTS
from config import settings
import  pandas as pd
import logging

client_id = settings.NCDR_CLIENT_ID

table_ids = FORM_TABLE_IDS
events = FORM_EVENTS

def get_element_info(xml_file, reference_value):
    tree = ET.parse(xml_file)
    root = tree.getroot()
    for element in root.findall(".//element"):
        if element.get("reference") == reference_value:
            code = element.get("code")
            name = element.find("name").text if element.find("name") is not None else "N/A"
            return {"code": code, "name": name}
    return None

def update_xml_value(xml_file, code, display_name, new_value):
    tree = ET.parse(xml_file)
    root = tree.getroot()
    if "xmlns:xsd" not in root.attrib:
        root.set("xmlns:xsd", "http://www.w3.org/2001/XMLSchema")

    for element in root.findall(".//element"):
        if element.get("code") == code and element.get("displayName") == display_name:
            value_element = element.find("value")
            if value_element is not None:
                xsi_type = value_element.attrib.get("{http://www.w3.org/2001/XMLSchema-instance}type")
                if xsi_type == "CD":
                    value_element.attrib.pop("value", None)
                    continue
                if xsi_type == "BL":
                    if str(new_value).lower() in ["yes", "true"]:
                        new_value = "true"
                    elif str(new_value).lower() in ["no", "false"]:
                        new_value = "false"
                    else:
                        new_value = None
                elif xsi_type == "DT":
                    try:
                        from datetime import datetime
                        date_obj = datetime.strptime(str(new_value), "%Y-%m-%d")
                        new_value = date_obj.strftime("%Y-%m-%d")
                    except ValueError:
                        new_value = None
                if new_value is None or str(new_value).strip() == "":
                    value_element.attrib.pop("value", None)
                else:
                    value_element.set("value", new_value)

    # for value_element in root.iter("value"):
    #     xsi_type = value_element.attrib.get("{http://www.w3.org/2001/XMLSchema-instance}type")
    #     val = value_element.attrib.get("value")

    #     if val is None or val.strip() == "" or xsi_type == "CD":
    #         value_element.attrib.pop("value", None)

    tree.write(xml_file, encoding='utf-8', xml_declaration=True)
    return xml_file


def get_field_ids_and_values(data: dict, case_id):
    field_data = []
    updated_xml_path = None

    xml_folder = os.path.abspath(rf"v2/data_sources/xml/{case_id}")
    os.makedirs(xml_folder,exist_ok = True)
    original_xml_path = os.path.abspath("v2/data_sources/ncdr/generated_registry_document.xml")
    copied_xml_path = os.path.join(xml_folder, f"LAAO{client_id}-2025Q1.xml")

    shutil.copy(original_xml_path, copied_xml_path)

    def extract_field_data(obj):
        nonlocal updated_xml_path
        if isinstance(obj, dict):
            if "field_id" in obj and "value" in obj:
                field_data.append({"field_id": obj["field_id"], "value": obj["value"], "options": obj.get("options",None), "input_type": obj.get("input_type")})
                if field_data:
                    dict1 = get_element_info(os.path.abspath("v2/data_sources/ncdr/dict.xml"), obj["field_id"])
                    if dict1:
                        updated_xml_path = update_xml_value(copied_xml_path, dict1["code"], dict1["name"], obj["value"])
            for key in obj:
                extract_field_data(obj[key])
        elif isinstance(obj, list):
            for item in obj:
                extract_field_data(item)
    extract_field_data(data)
    if updated_xml_path is None:
        logging.info("ERROR: No XML updates were made")
        raise ValueError("Failed to update XML file")
    logging.info(f"updated xml path : {updated_xml_path}")

    return updated_xml_path

def validate_xml(xml_file):
    errors = []
    xsd_file = os.path.abspath("v2/data_sources/ncdr/RTD.xsd")
    with open(xsd_file, 'rb') as f:
        xsd_doc = etree.parse(f)
        xsd_schema = etree.XMLSchema(xsd_doc)

    with open(xml_file, 'rb') as f:
        xml_doc = etree.parse(f)
    if xsd_schema.validate(xml_doc):
        logging.info("✅ XML is valid against the XSD.")
        return errors
    else:
        logging.info("❌ XML validation failed.")
        for error in xsd_schema.error_log:
            logging.info(f"Line {error.line}: {error.message}")
            errors.append(f"Line {error.line}: {error.message}")
        return errors

def compute_hash(plain_text: str, hash_algorithm: str = None, salt_bytes: bytes = None) -> str:
    if salt_bytes is None:
        min_salt_size = 4
        max_salt_size = 8
        salt_size = secrets.randbelow(max_salt_size - min_salt_size + 1) + min_salt_size
        salt_bytes = secrets.token_bytes(salt_size)
    plain_text_bytes = plain_text.encode('utf-8')
    plain_text_with_salt_bytes = plain_text_bytes + salt_bytes
    hash_algorithm = (hash_algorithm or '').upper()
    hash_algorithms = {
        'SHA1': hashlib.sha1,
        'SHA256': hashlib.sha256,
        'SHA384': hashlib.sha384,
        'SHA512': hashlib.sha512,
        'MD5': hashlib.md5
    }
    hash_func = hash_algorithms.get(hash_algorithm, hashlib.md5)
    hash_bytes = hash_func(plain_text_with_salt_bytes).digest()
    hash_with_salt_bytes = hash_bytes + salt_bytes
    hash_value = base64.b64encode(hash_with_salt_bytes).decode('utf-8')

    return hash_value

def zip_and_encrypt_file(file_path,case_id, secrets):
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File '{file_path}' not found.")
    zip_folder = os.path.abspath(f"v2/data_sources/xml/{case_id}")
    os.makedirs(zip_folder,exist_ok = True)
    zip_file_name = f"LAAO{client_id}-2025Q1.zip"
    zip_file_path = os.path.join(zip_folder, zip_file_name)

    with pyzipper.AESZipFile(zip_file_path, 'w', compression=pyzipper.ZIP_DEFLATED, encryption=pyzipper.WZ_AES) as zipf:
        zipf.setpassword(secrets.encode())
        zipf.write(file_path, os.path.basename(file_path))
    logging.info(f"Encrypted zip file created: {zip_file_path}")

    return zip_file_path

def unzip_and_decrypt_file(zip_file, secrets):
    with pyzipper.AESZipFile(zip_file, 'r', compression=pyzipper.ZIP_DEFLATED, encryption=pyzipper.WZ_AES) as zipf:
        zipf.setpassword(secrets.encode())
        file_name = zipf.namelist()[0]  # Assuming only one file
        logging.info(f"Extracting file: {file_name}" )
        with zipf.open(file_name) as extracted_file:
            extracted_text = extracted_file.read().decode("utf-8")

    return extracted_text


def construct_ncdr_xml(data_dict: dict, case_id: str) -> str:
    xml_folder = os.path.abspath(rf"v2/data_sources/xml/{case_id}")
    os.makedirs(xml_folder,exist_ok = True)
    year = datetime.datetime.now().year
    op_file_path = f"{xml_folder}/LAAO{client_id}-{year}Q1.xml"
    processed_data = extract_fields_with_label(data_dict)
    process_ncdr_data(processed_data, op_file_path)

    return op_file_path


def extract_fields_with_label(data_dict):
    """
    Traverse through a dictionary and extract fields with a 'label' key.

    Args:
        data_dict (dict): The input dictionary to traverse

    Returns:
        list: A list of dictionaries containing field_id, value, options, and input_type
    """
    result = []

    def traverse(obj, path=None):
        if path is None:
            path = []

        if isinstance(obj, dict):
            # Check if this is a field with a label
            if 'label' in obj:
                if obj.get('field_id', None) not in table_ids:
                    field_info = {
                        'field_id': obj.get('field_id', None),
                        'label': obj.get('label', None),
                        'value': obj.get('value', None),
                        'options': obj.get('options', None),
                        'input_type': obj.get('input_type', None)
                    }
                    result.append(field_info)

            # Continue traversing all key-value pairs
            for key, value in obj.items():
                if key == "field_id" and value in table_ids:
                    result.append(obj)
                    break
                new_path = path + [key]
                traverse(value, new_path)

    traverse(data_dict)
    return result

def remove_tag(xml_tree: ET.ElementTree, tag_name: str):

    for parent in xml_tree.getroot():
        for element in parent.findall(f".//{tag_name}"):
            parent.remove(element)

def remove_empty_tags(xml_tree: ET.ElementTree, tag_name: str):
    """
    Removes all instances of the specified tag from the XML tree if they have no child elements,
    regardless of their depth in the tree.

    Parameters:
    xml_tree (ElementTree): The XML tree to modify
    tag_name (str): The name of the tag to remove if empty

    Returns:
    ElementTree: The modified XML tree
    """
    root = xml_tree.getroot()

    _remove_empty_tags_recursive(root, tag_name)

    return xml_tree

def _remove_empty_tags_recursive(element, tag_name):
    """
    Recursively process elements to find and remove empty tags.
    """
    for child in list(element):
        _remove_empty_tags_recursive(child, tag_name)

    for child in list(element):
        if child.tag == tag_name and len(list(child)) == 0:
            element.remove(child)


def update_xml_element_by_attributes(tree: ET.ElementTree | ET.Element, attribute_conditions: dict, section_condition: dict, new_value):
    """
    Updates XML <element> tags based on attribute match and sets the <value> tag's 'value' attribute.

    Parameters:
        tree (ElementTree): The parsed XML tree.
        attribute_conditions (dict): Attribute conditions to match in <element> tags.
        new_value (str): Value to set in the <value> child element.
    """
    sections = get_all_section(tree, section_condition) or [tree]
    for section in sections:
        for element in section.findall(".//element"):
            if all(element.get(attr) == val for attr, val in attribute_conditions.items()):
                value_tags = element.findall("value")
                for v in value_tags:
                    v.set("value", str(new_value))
                logging.info(f"Updated element with {attribute_conditions} to value: {new_value}")



def filter_element_values_by_code(xml_tree: ET.ElementTree | ET.Element, condition: dict, section_condition: dict, allowed_display_names: list[str]):
    """
    Keeps only <value> tags whose displayName is in the allowed list,
    under an <element> tag that matches the given condition.

    Parameters:
        xml_tree (ElementTree): The parsed XML tree.
        condition (dict): Attribute conditions to match on <element> tag (e.g. {"code": "123", "displayName": "Race"}).
        allowed_display_names (list of str): List of displayName values to keep in <value> tags.
    """
    sections = get_all_section(xml_tree, section_condition) or [xml_tree]
    allowed_set = {name.strip().lower() for name in allowed_display_names}

    for section in sections:
        for element in section.findall(".//element"):
            if all(element.get(attr) == val for attr, val in condition.items()):
                value_tags = element.findall("value")

                for value_tag in value_tags:
                    value_display = value_tag.get("code", "").strip().lower()
                    if value_display not in allowed_set:
                        element.remove(value_tag)
                        logging.info(f"Removed <value displayName='{value_display}'> (not in allowed list)")

                logging.info(f"✅ Final allowed displayNames: {allowed_display_names}")
                break

def remove_tag_by_condition(xml_tree: ET.ElementTree, tag_name: str, condition: dict, section_condition: dict):
    """
    Removes the first tag with the given tag name and attribute condition from the XML tree.

    Parameters:
        xml_tree (ElementTree): The parsed XML tree.
        tag_name (str): The name of the tag to search for (e.g., 'element', 'value').
        condition (dict): Attributes to match (e.g., {"code": "1234", "displayName": "Race"}).
    """
    removed = False
    sections = get_all_section(xml_tree, section_condition)
    for parent in sections:
        for child in list(parent):  # use list() to avoid mutation during iteration
            if child.tag == tag_name and all(child.get(k) == v for k, v in condition.items()):
                parent.remove(child)
                logging.info(f"Removed <{tag_name}> with attributes: {condition}")
                removed = True
                break
        if removed:
            break

    if not removed:
        logging.info(f"No <{tag_name}> tag found with attributes: {condition}")

def remove_attribute(xml_tree: ET.ElementTree, tag_name: str, attribute: str):
    xml = xml_tree.getroot()

    for element in xml.iter(tag_name):
        attribute_value = element.attrib.get(attribute)

        if attribute_value is None or attribute_value.strip() == "":
            element.attrib.pop(attribute, None)

def get_all_section(xmltree: ET.ElementTree | ET.Element, condition: dict, remove: bool = False) -> list[ET.Element]:
    sections = []

    if isinstance(xmltree, ET.Element):
        root = xmltree
    else:
        root = xmltree.getroot()

    # Create a parent map for efficient parent lookup
    parent_map = {c: p for p in root.iter() for c in p}

    section_elements = root.findall(".//section")

    for section in section_elements:
        if all(section.get(attr) == val for attr, val in condition.items()):
            sections.append(section)

    if remove and sections:
        for section in sections:
            parent = parent_map.get(section)
            if parent is not None:
                parent.remove(section)

    return sections

def remove_event_tags(xmltree: ET.ElementTree | ET.Element, condition: dict, section_condition: dict):


    sections = get_all_section(xmltree, section_condition) or [xmltree]

    for section in sections:
        elements = section.findall(".//element")
        section_flag = 0
        for ele in elements:
            all_value = ele.findall(".//value")
            for m_val in all_value:
                if section_flag == 0:
                    med = all(m_val.get(attr) == it_val for attr, it_val in condition.items())
                    if med:
                        section.clear()
                        section_flag = 1
                        break
            if section_flag ==1:
                break
        if section_flag==1:
            break


def handle_table_fields(xmltree: ET.ElementTree, elements, selections, field_id: int, data: dict, key_element: str, section_condition: dict):
    table_data = data.get(key_element)
    if not table_data: return

    sections = get_all_section(xmltree, section_condition)
    for key, val in table_data.items():

        dos_id = val.get("field_id")
        value = val.get("value")

        event_date = None
        if 'if_yes' in val and value in ["yes", "Yes", True, "true"]:
            event_date = val.get('if_yes',{}).get("value")

        selection_values = selections[(selections['Element Reference'] == field_id) & (selections['Selection Name'].str.strip() == val.get("label"))]

        if not selection_values.empty:

            selection_values = selection_values.iloc[0]
            medicine = {
                "displayName": str(selection_values['Selection Name']),
                "code": str(selection_values['Code']),
                "codeSystem": str(selection_values['Code System'])
            }
            if not value:
                logging.info(f"Removing... {medicine}")
                remove_event_tags(xmltree, medicine, section_condition)
                continue
            else:
                logging.info(f"value is in the element...")

            if str(field_id) in events:
                for section in sections:
                    elements = section.findall(".//element")
                    section_flag = 0

                    for ele in elements:
                        all_value = ele.findall(".//value")
                        for m_val in all_value:
                            if section_flag == 0:
                                med = all(m_val.get(attr) == it_val for attr, it_val in medicine.items())
                                if med:
                                    section_flag = 1
                            else:
                                if m_val.attrib.get("{http://www.w3.org/2001/XMLSchema-instance}type") == "BL":
                                    m_val.set("value", "true" if value in ["true", True, "yes", "Yes"] else "false")
                                elif m_val.attrib.get("{http://www.w3.org/2001/XMLSchema-instance}type") == "DT":
                                    m_val.set("value", event_date if event_date else "")
                    if section_flag == 1:
                        break
            else:
                value_selection = selections[(selections['Element Reference'] == int(dos_id)) & (selections['Selection Name'].str.strip() == value)]
                if not value_selection.empty:

                    value_selection = value_selection.iloc[0]
                    value_set = {
                        # "displayName": str(value_selection['Selection Name']),
                        "code": str(value_selection['Code']),
                        "codeSystem": str(value_selection['Code System'])
                    }

                    for section in sections:
                        elements = section.findall(".//element")
                        section_flag = 0

                        for ele in elements:
                            all_value = ele.findall(".//value")
                            for m_val in all_value:
                                if section_flag == 0:
                                    med = all(m_val.get(attr) == it_val for attr, it_val in medicine.items())
                                    if med:
                                        section_flag = 1
                                else:
                                    dosage = all(m_val.get(attr) == it_val for attr, it_val in value_set.items())
                                    if not dosage:
                                        ele.remove(m_val)
                        if section_flag == 1:
                            break

def add_element_with_device_mapping(element: ET.Element, elements: pd.DataFrame, selection: pd.DataFrame, device_elements: pd.DataFrame, data: dict) -> ET.Element:
    """
    Enhanced version of add_element that uses device_elements.csv for device mapping
    """
    for k, v in data.items():
        field_id = int(v.get("field_id", 0))
        value = v.get("value", 0)
        element_data = elements[elements['Element Reference'] == field_id]

        if element_data.empty:
            logging.info(f"No element mapping found for field_id: {field_id}")
            continue

        element_data = element_data.iloc[0]

        condition = {
            "displayName": str(element_data['Name']),
            "code": str(element_data['Code']),
            "codeSystem": str(element_data['Code System'])
        }

        if not value:
            logging.info(f"Removing... {condition}")
            remove_event_tags(element, condition, {})
            continue

        value_type = str(element_data['Data Type'])

        # Handle boolean values
        if value_type == 'BL':
            if value:
                if value in ["Yes", "yes", "true", True]:
                    value = 'true'
                else:
                    value = 'false'

        # Handle coded values - check if this is a device field
        if value_type == 'CD' and field_id == 14841:  # Device ID field
            # Look up the device in device_elements.csv
            device_data = device_elements[
                device_elements['deviceName'].str.strip() == value.strip()
            ]

            if not device_data.empty:
                device_data = device_data.iloc[0]

                # Create Device element with proper NCDR codes
                device_element = create_element('element', {
                    "code": str(element_data['Code']),  # 63653004
                    "codeSystem": str(element_data['Code System']),  # 2.16.840.1.113883.6.96
                    "displayName": str(element_data['Name'])  # Device
                })

                # Create value element with device info from CSV
                device_value_element = create_element('value', {
                    "xsi:type": "CD",
                    "code": str(device_data['deviceID']),
                    "codeSystem": str(device_data['codeSystem']),  # 2.16.840.1.113883.3.3478.6.1.109
                    "displayName": f"{device_data['deviceName']} ({device_data['modelNumber']})"
                })

                device_element.append(device_value_element)
                element.append(device_element)

                logging.info(f"✅ Mapped device: {value} -> deviceID: {device_data['deviceID']}")
                continue
            else:
                logging.warning(f"⚠️ Device '{value}' not found in device_elements.csv")
                # Fall back to original selection-based logic

        # Original logic for non-device CD fields or fallback
        if value_type == 'CD':
            selection_values = selection[(selection['Element Reference'] == field_id) & (selection['Selection Name'].str.strip().isin(value if isinstance(value, list) else [value]))]

            if not selection_values.empty:
                codes = selection_values['Code'].to_list()
                filter_element_values_by_code(element, condition, {}, codes)
        else:
            update_xml_element_by_attributes(element, condition, {}, value)

    return element

def add_element(element: ET.Element, elements: pd.DataFrame, selection: pd.DataFrame, data: dict) -> ET.Element:

    for k, v in data.items():
        field_id = int(v.get("field_id", 0))
        value = v.get("value", 0)
        element_data = elements[elements['Element Reference'] == field_id]


        if element_data.empty:
            logging.info(f"No element mapping found for field_id: {field_id}")
            return
        element_data = element_data.iloc[0]

        condition = {
                "displayName": str(element_data['Name']),
                "code": str(element_data['Code']),
                "codeSystem": str(element_data['Code System'])
            }
        if not value:
            logging.info(f"Removing... {condition}")
            remove_event_tags(element, condition, {})
            continue
        value_type = str(element_data['Data Type'])
        if value_type == 'BL':
            if value:
                if value in ["Yes", "yes", "true", True]:
                    value = 'true'
                else:
                    value = 'false'

        if value_type == 'CD':
            selection_values = selection[(selection['Element Reference'] == field_id) & (selection['Selection Name'].str.strip().isin(value if isinstance(value, list) else [value]))]

            if not selection_values.empty:
                codes = selection_values['Code'].to_list()
                filter_element_values_by_code(element, condition, {}, codes)

        else:
            update_xml_element_by_attributes(element, condition, {}, value)
    return element


def create_element(tag_name: str, attributes: dict)-> ET.Element:
    return ET.Element(tag_name, attributes)


def process_ncdr_data(field_result: list[dict], op_file_path: str):
    processed = []

    selection_path = os.path.abspath('v2/data_sources/ncdr/selections.csv')
    elements_path = os.path.abspath('v2/data_sources/ncdr/elements.csv')
    access_system_elements_path = os.path.abspath('v2/data_sources/ncdr/access_system_elements.csv')
    device_elements_path = os.path.abspath('v2/data_sources/ncdr/device_elements.csv')

    # Try different encodings for CSV files to handle various file formats
    def read_csv_with_encoding(file_path):
        encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252', 'iso-8859-1']
        for encoding in encodings:
            try:
                return pd.read_csv(file_path, encoding=encoding)
            except UnicodeDecodeError:
                continue
        # If all encodings fail, raise the original error
        return pd.read_csv(file_path)

    selection = read_csv_with_encoding(selection_path)
    elements = read_csv_with_encoding(elements_path)
    access_system_elements = read_csv_with_encoding(access_system_elements_path)
    device_elements = read_csv_with_encoding(device_elements_path)

    basexml_path = os.path.abspath('v2/data_sources/ncdr/generated_registry_document.xml')
    basexml = ET.parse(basexml_path)

    for fields in field_result:
        field_id = fields.get("field_id")
        label = fields.get("label")
        value = fields.get("value")

        logging.info(f"🔎 Processing: field_id = {field_id} | label = {label}")

        try:
            field_id_int = int(field_id)
        except (ValueError, TypeError):
            print(f"❌ Invalid field_id: {field_id}")
            if field_id not in table_ids:
                continue
            field_id_int = None

        if field_id_int is not None:
            element_data = elements[elements['Element Reference'] == field_id_int]

            if element_data.empty:
                logging.info(f"No element mapping found for field_id: {field_id}")
                continue

            # Extract values as strings
            element_data = element_data.iloc[0]

        if field_id not in table_ids:
            # continue
            section_condition = {
                "code": element_data["Section Code"]
            }
            condition = {
                "displayName": str(element_data['Name']),
                "code": str(element_data['Code']),
                "codeSystem": str(element_data['Code System'])
            }
            value_type = str(element_data['Data Type'])
            if condition not in processed:
                if value_type == 'BL':
                    if value:
                        if isinstance(value, bool):
                            value = 'true' if value else 'false'
                        else:
                            value = 'true' if value.lower() == 'yes' else 'false'

                if 'value' in fields and not value:
                    remove_tag_by_condition(basexml, 'element', condition, section_condition)
                elif value_type == 'CD':
                    selection_values = selection[(selection['Element Reference'] == field_id_int) & (selection['Selection Name'].str.strip().isin(value if isinstance(value, list) else [value]))]

                    if not selection_values.empty:
                        codes = selection_values['Code'].to_list()
                        filter_element_values_by_code(basexml, condition, section_condition, codes)

                else:
                    update_xml_element_by_attributes(basexml, condition, section_condition, value)

                condition.update({"section_code": str(element_data["Section Code"])})
                processed.append(condition)
        elif field_id == '14839_container':
            access_sys_elements_list = []
            field_id_int = 14839
            access_sys_data = elements[elements['Element Reference'] == field_id_int]
            device_data = elements[elements['Element Reference'] == 14841]

            if access_sys_data.empty and device_data.empty:
                logging.info(f"❌ No element mapping found for field_id: {field_id}")
                continue

            access_sys_data = access_sys_data.iloc[0]
            device_data = device_data.iloc[0]
            access_conditon = {
                "code": access_sys_data['Section Code'],
                "displayName": access_sys_data['Section Display Name']
            }
            access_sys = get_all_section(basexml, access_conditon, True)

            if access_sys:
                device_condition = {
                    "code": device_data['Section Code']
                }

                device_sec = get_all_section(access_sys[0], device_condition, True)
                if device_sec:
                    for access in fields.get("items", []):
                        access_sys_element = create_element('section', access_conditon)

                        # Process Access System Counter
                        access_system_counter_id = access.get("access_system_counter", {}).get("field_id", "")
                        access_system_counter_value = access.get("access_system_counter", {}).get("value", "")

                        if access_system_counter_id:
                            access_system_counter_data = elements[elements['Element Reference'] == int(access_system_counter_id)]
                            if not access_system_counter_data.empty:
                                access_system_counter_data = access_system_counter_data.iloc[0]
                                counter_element = create_element('element', {
                                    "code": access_system_counter_data['Code'],
                                    "codeSystem": access_system_counter_data['Code System'],
                                    "displayName": access_system_counter_data['Name']
                                })
                                counter_element.append(
                                    create_element('value', {
                                        "xsi:type": access_system_counter_data['Data Type'],
                                        "value": access_system_counter_value
                                    })
                                )
                                access_sys_element.append(counter_element)

                        # Process Access System using access_system_elements.csv
                        access_system = access.get("access_system_counter", {}).get("template", {}).get("access_system", {})
                        access_system_value_id = access_system.get("field_id", "")
                        access_system_value = access_system.get("value", "")

                        if access_system_value_id and access_system_value:
                            # Look up the access system in the access_system_elements.csv
                            access_device_data = access_system_elements[
                                access_system_elements['deviceName'].str.strip() == access_system_value.strip()
                            ]

                            if not access_device_data.empty:
                                access_device_data = access_device_data.iloc[0]

                                # Create Access System element with proper NCDR codes
                                access_element = create_element('element', {
                                    "code": "112000002110",  # NCDR Access System code
                                    "codeSystem": "2.16.840.1.113883.3.3478.6.1",  # NCDR code system
                                    "displayName": "Access System"
                                })

                                # Create value element with device info from CSV
                                value_element = create_element('value', {
                                    "xsi:type": "CD",
                                    "code": str(access_device_data['deviceID']),
                                    "codeSystem": str(access_device_data['codeSystem']),
                                    "displayName": f"{access_device_data['deviceName']} ({access_device_data['modelNumber']})"
                                })

                                access_element.append(value_element)
                                access_sys_element.append(access_element)

                                logging.info(f"✅ Mapped access system: {access_system_value} -> deviceID: {access_device_data['deviceID']}")
                            else:
                                logging.warning(f"⚠️ Access system '{access_system_value}' not found in access_system_elements.csv")
                                # Fallback to original logic if not found in CSV
                                access_system_data = elements[elements['Element Reference'] == int(access_system_value_id)]
                                if not access_system_data.empty:
                                    access_system_data = access_system_data.iloc[0]
                                    fallback_element = create_element('element', {
                                        "code": access_system_data['Code'],
                                        "codeSystem": access_system_data['Code System'],
                                        "displayName": access_system_data['Name']
                                    })
                                    fallback_element.append(create_element('value', {
                                        "xsi:type": access_system_data['Data Type'],
                                        "displayName": access_system_value
                                    }))
                                    access_sys_element.append(fallback_element)

                        # Process devices
                        devices: list[dict] = access_system.get("template", {}).get("devices", []).get("template", [])
                        for dev in devices:
                            d_temp = dev.get("device_counter", {}).pop("template", {})
                            dev.update(d_temp)
                            dev_element = add_element_with_device_mapping(device_sec[0].__copy__(), elements, selection, device_elements, dev)
                            access_sys_element.append(dev_element)

                        access_sys_elements_list.append(access_sys_element)

                    if access_sys_elements_list:
                        section = get_all_section(basexml, {"code": "PROCINFO"})
                        if section:
                            section[0].extend(access_sys_elements_list)
        else:
            condition = {
                "displayName": str(element_data['Section Display Name']),
                "code": str(element_data['Section Code']),
                # "codeSystem": str(element_data['Code System'])
            }

            mapping = {
                "6985": "medications",
                "12153": "elements",
                "10200": "medications",
                "14940": "medications",
                "11990": "elements",
                "14948": "event_occurred",
                "15006": "medications"
            }
            mapper = mapping.get(field_id)
            if mapper:
                if field_id in events:
                    event_data = fields.get(mapper, {}).copy()
                    fields.pop(mapper,{})
                    for event in event_data.values():
                        fields.setdefault(mapper, {}).update(event)
                handle_table_fields(basexml, elements , selection, field_id_int, fields, mapper ,condition)
            else:
                logging.info("Mapper Element not defined...")

    remove_attribute(basexml, "value", "value")
    remove_empty_tags(basexml, "section")
    remove_empty_tags(basexml, "element")
    remove_tag(basexml, 'followup')
    basexml.write(op_file_path, encoding="utf-8", xml_declaration=True)